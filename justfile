# #!/usr/bin/env just
#
# Cross-platform justfile for hallite-configurator-api
#
# For Unix/Mac/Linux: Use the default commands (e.g., `just up`, `just down`, `just env-file`)
# For Windows: Use the Windows-specific variants where needed (e.g., `just up-windows`, `just down-windows`, `just env-file-windows`)
#
# The justfile automatically uses PowerShell on Windows and bash on Unix systems

# Set Windows-specific shell (bash is used by default on Unix systems)
set windows-shell := ["powershell.exe", "-c"]

default:
    @just --list

#####################
# LOCAL DEVELOPMENT #
#####################

dev-venv:
    uv venv
    uv sync --all-groups --compile-bytecode

dev-dependencies:
    uv sync --all-groups --compile-bytecode

env-file:
    doppler secrets download --no-file --format docker > .env

env-file-windows:
    doppler secrets download --no-file --format docker | Out-File -FilePath .env -Encoding utf8

dev-server:
    doppler run -- uv run manage.py runserver_plus 0.0.0.0:9090


#######
# APP #
#######

app-install-dependencies:
    uv sync --no-dev --compile-bytecode

app-upgrade-dependencies:
    uv lock --upgrade

pre-commit:
    uv run pre-commit run --all-files

app-load-base-data:
     uv run manage.py loaddata config/fixtures/group.yaml
     uv run manage.py loaddata config/fixtures/user.yaml
     uv run manage.py loaddata config/fixtures/admin_interface.yaml


##########
# CHECKS #
##########

check:
    just check-black
    just check-isort
    just check-flake8
    just check-pylint
    just check-js-css
    just check-autoflake
    just check-types

check-djlint:
    uv run djlint ./ --check

check-flake8:
    uv run flake8 ./

check-isort:
    uv run isort ./ --check

check-black:
    uv run black ./ --check

check-pylint:
    uv run pylint --load-plugins pylint_django ./

check-autoflake:
    uv run autoflake --check --quiet --recursive ./

check-types:
    uv run ty check


##########
# FORMAT #
##########

format:
    just format-black
    just format-isort
    just format-autoflake
    just format-whitespace

format-isort:
    uv run isort ./

format-black:
    uv run black ./

format-autoflake:
    uv run autoflake --in-place --recursive ./

format-whitespace:
    find . -name "*.py" -not -path "./.venv/*" -not -path "./node_modules/*" -exec sed -i '' 's/[[:space:]]*$//' {} \;

format-whitespace-windows:
    powershell -Command "Get-ChildItem -Path . -Filter '*.py' -Recurse | Where-Object { $_.FullName -notmatch '\\.venv' -and $_.FullName -notmatch 'node_modules' } | ForEach-Object { (Get-Content $_.FullName) -replace '\\s+$', '' | Set-Content $_.FullName }"


###########
# TESTING #
###########

# Run tests with default settings from pyproject.toml
test *args='':
    doppler run --config test -- uv run pytest -v "{{ args }}"

# Run tests with debug logging
test-verbose *args='':
    doppler run --config test -- uv run pytest --log-cli-level=DEBUG "{{ args }}"

# Run tests with coverage reporting
test-coverage:
    doppler run --config test -- uv run pytest --log-cli-level=DEBUG --cov --cov-config=pyproject.toml --cov-report=term --cov-report=html


##########
# CI/CD #
#########

git-version:
    #!/usr/bin/env bash
    set -e
    # Try to get the exact tag for the current commit
    TAG=$(git describe --tags --exact-match HEAD 2>/dev/null || echo "")

    if [ -n "$TAG" ]; then
        # Strip leading 'v' if present
        echo "${TAG#v}"
    else
        # Fallback to branch-commit if no exact tag is found
        BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
        COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
        echo "${BRANCH}-${COMMIT}"
    fi

tag-release:
    #!/usr/bin/env bash
    set -e
    echo "Checking current branch..."
    BRANCH=$(git rev-parse --abbrev-ref HEAD)
    if [ "$BRANCH" != "main" ]; then
        echo "Not on main branch. Current branch is $BRANCH.";
        exit 1;
    fi
    echo "Checking for uncommitted changes..."
    if ! git diff --quiet HEAD; then
        echo "There are uncommitted changes.";
        exit 1;
    fi
    echo "Checking for staged changes..."
    if ! git diff --cached --quiet; then
        echo "There are staged changes.";
        exit 1;
    fi
    read -p "Enter version (last $(git describe --tags --abbrev=0)): " VERSION;

    echo "Checking if tag v$VERSION already exists..."
    if [ -n "$(git tag -l "v$VERSION")" ]; then
        echo "Error: Tag v$VERSION already exists."
        # Optional: Revert pyproject.toml change if tag exists and we are aborting
        # git checkout -- pyproject.toml
        exit 1;
    fi

    echo "Locking dependencies with uv..."
    uv lock

    echo "Tagging release v$VERSION..."
    git tag "v$VERSION"

    echo "Pushing tag v$VERSION to origin..."
    git push origin "v$VERSION"


##############
# DEPLOYMENT #
##############

tls:
    doppler secrets get TLS_CERT --plain > tls/cert.pem
    doppler secrets get TLS_KEY --plain > tls/key.pem

# Unix/Mac server commands (default)
up-x *args='':
    just tls
    doppler run -- uv run gunicorn -c python:config.gunicorn config.wsgi --daemon {{ args }}

down-x:
    pkill -f gunicorn || echo "No gunicorn processes found"

# Windows server commands
up *args='':
    doppler run -- uv run python -m config.waitress {{ args }}

down:
    @$procs = @(Get-CimInstance Win32_Process | Where-Object { $_.CommandLine -like "*python*-m*config.waitress*" -and $_.CommandLine -notlike "*Get-CimInstance*" }); if ($procs.Count -gt 0) { $procs | ForEach-Object { taskkill /f /pid $_.ProcessId 2>$null } } else { Write-Host "No waitress processes found" }

monitor-dialogs:
    doppler run -- uv run manage.py monitor_vba_dialogs 

cleanup-sessions:
    doppler run -- uv run manage.py cleanup_excel_sessions

cleanup-processes:
    doppler run -- uv run manage.py cleanup_excel_processes

tls-windows:
    doppler secrets get TLS_CERT --plain | Out-File -FilePath tls/cert.pem -Encoding utf8
    doppler secrets get TLS_KEY --plain | Out-File -FilePath tls/key.pem -Encoding utf8

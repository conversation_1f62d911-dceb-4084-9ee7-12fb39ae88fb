#!/bin/bash
set -e

echo "Setting up Hallite Configurator API development environment..."

# Update system packages
sudo apt-get update -y

# Install Python 3.13 and development tools
sudo apt-get install -y python3.13 python3.13-dev python3.13-venv python3-pip build-essential

# Install UV package manager
curl -LsSf https://astral.sh/uv/install.sh | sh
echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> $HOME/.profile
export PATH="$HOME/.cargo/bin:$PATH"

# Install Just task runner
curl --proto '=https' --tlsv1.2 -sSf https://just.systems/install.sh | bash -s -- --to ~/.local/bin
echo 'export PATH="$HOME/.local/bin:$PATH"' >> $HOME/.profile
export PATH="$HOME/.local/bin:$PATH"

# Navigate to project directory
cd /mnt/persist/workspace

# Set up development virtual environment using justfile
echo "Creating development virtual environment..."
just dev-venv

# Activate virtual environment for current session
source .venv/bin/activate

# Install application dependencies
echo "Installing application dependencies..."
just app-install-dependencies

# Create necessary directories
mkdir -p db logs media static uploads tls

# Set up Django database
echo "Setting up Django database..."
uv run python manage.py migrate --run-syncdb

# Load base data (users, groups, admin interface theme)
echo "Loading base data..."
just app-load-base-data

# Run code quality checks to ensure everything is working
echo "Running code quality checks..."
just check

echo ""
echo "✅ Development environment setup complete!"
echo ""
echo "Available commands:"
echo "  just --list                    # Show all available commands"
echo "  just dev-server               # Start development server"
echo "  just test                     # Run tests"
echo "  just format                   # Format code"
echo "  just check                    # Run all code quality checks"
echo ""
echo "To start development:"
echo "  1. Activate virtual environment: source .venv/bin/activate"
echo "  2. Start development server: just dev-server"
echo "  3. Visit http://localhost:9090/api/docs for API documentation"
echo ""
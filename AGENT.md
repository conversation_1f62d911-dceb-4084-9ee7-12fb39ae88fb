# Coding Rules for Hallite Excel API

**Golden rule**: 

- When unsure about business logic, architecture choices, trade-offs, or implementation details—ALWAYS consult the developer.
- Do not add docstrings unless the method is complex and what it does is not unintuitive 
- Do not add code comments unless the behavior is unintuitive or extremely advanced
---

## 1. Technology Stack
- **Backend**: Django 5.2+ with Django Ninja REST API, Python 3.13+
- **Excel Automation**: xlwings for macro execution, pywinauto for error handling
- **Database**: SQLite
- **Package Management**: UV for fast Python dependency management
- **Task Runner**: Just (justfile) for development tasks

## 2. Core Rules

**AI MAY:**
- Generate code in Django app directories
- Follow lint/style configs (`just format`, `just check`)
- Use existing patterns and conventions

**AI MUST NOT:**
- Touch test files, fixtures, or migrations without permission
- Ignore linting errors or change code style
- Make large changes (>5 files or >200 LOC) without confirmation
- Create new architectural patterns without discussion

---
 
## 3. Key Commands & Standards

**Essential Commands:**
```bash
just format                     # Format code (black, isort)
just check                      # Run all linting checks
just test                       # Run tests with pytest
just dev-server                 # Run development server
```

**Code Style:**
- Python 3.13+, Django 5.2+, type hints preferred
- Black formatting (150-char lines), isort imports
- `snake_case` functions/variables, `PascalCase` classes
- Pydantic models for API schemas
- Clear docstrings for service methods

---

## 4. Excel API Specific Guidelines

**Service Architecture:**
- Keep Excel automation (xlwings) separate from business logic
- Use simple constructor injection for dependencies (no DI frameworks)
- Services: ExcelService, WorkbookState, SessionManager, ResourceMonitor
- Handle COM errors and Excel process cleanup properly

**Session Management:**
- UUID-based temporary workbook copies in %TEMP%
- 5-minute session lifespan with automatic cleanup
- Thread-safe operations for concurrent requests

**Error Handling:**
- Capture VBA dialogs with pywinauto
- Handle specific sheet error conditions (costpriceevco=0, partdesc messages)
- 5-second timeout for macro execution
- Proper resource cleanup on all error paths

**Anchor Comments:**
Use `# AIDEV-NOTE:`, `# AIDEV-TODO:` for important context that can be grep'd:
- Complex business logic
- Performance-critical sections
- Error handling patterns
- Resource management

---

## 5. Testing & Implementation Rules

**Testing Philosophy**: Tests encode human intent. AI implements business logic to make tests pass, never modifies tests.

**AI Must NEVER:**
- Modify test files, fixtures, or migrations
- Remove AIDEV- comments
- Make large refactors without approval
- Hardcode configurations (use environment variables)
- Bypass service layer patterns

**Commit Guidelines:**
- Tag AI commits: `feat: add Excel session management [AI]`
- One logical change per commit
- Run `just check` and `just test` before committing
- Explain the business impact in commit messages

## 6. Implementation Workflow

1. **Consult guidance** from AGENT.md and TODO.md
2. **Clarify ambiguities** with targeted questions
3. **Plan and break down** the task using project conventions
4. **For complex tasks**: Present plan for review before implementing
5. **Track progress** and re-plan if stuck
6. **Update documentation** and anchor comments when done
7. **Request review** from user


from django.conf import settings
from django.contrib import admin
from django.contrib.auth import views as auth_views
from django.urls import include, path, re_path
from django.views.static import serve
from ninja import NinjaAP<PERSON>
from ninja.security import django_auth

from configurator.api import APIKeyAuth, patch_ninja_api_error_handlers
from configurator.router import router

patch_ninja_api_error_handlers()

api = NinjaAPI(
    auth=[
        APIKeyAuth(),
        django_auth,
    ],
    urls_namespace="api",
)
api.add_router("", router)

urlpatterns = [
    path("", include("configurator.urls")),
    path("api/", api.urls),
    path(
        "manage/password_reset/",
        auth_views.PasswordResetView.as_view(extra_context={"site_header": admin.site.site_header}),
        name="admin_password_reset",
    ),
    path(
        "manage/password_reset/done/",
        auth_views.PasswordResetDoneView.as_view(extra_context={"site_header": admin.site.site_header}),
        name="password_reset_done",
    ),
    path(
        "reset/<uidb64>/<token>/",
        auth_views.PasswordResetConfirmView.as_view(extra_context={"site_header": admin.site.site_header}),
        name="password_reset_confirm",
    ),
    path(
        "reset/done/",
        auth_views.PasswordResetCompleteView.as_view(extra_context={"site_header": admin.site.site_header}),
        name="password_reset_complete",
    ),
    path("manage/", admin.site.urls),
    re_path(r"^media/(?P<path>.*)$", serve, {"document_root": settings.MEDIA_ROOT}),
]

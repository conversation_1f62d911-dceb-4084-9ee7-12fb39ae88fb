"""
Django settings.

Generated by 'django-admin startproject' using Django 5.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

import json
import logging
import os
from logging import basicConfig
from pathlib import Path

import logfire
import sentry_sdk
from dotenv import load_dotenv

from configurator.utils import app_version, strtobool

logger = logging.getLogger(__name__)

# Build paths inside the project like this: BASE_DIR / "subdir".
BASE_DIR = Path(__file__).resolve().parent.parent

dotenv_path = os.path.join(BASE_DIR, ".env")
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)

VERSION = app_version()

SECRET_KEY = os.getenv("DJANGO_SECRET_KEY")

DEBUG = bool(strtobool(os.getenv("DEBUG", "false")))

ENV = os.getenv("ENV", "production")

ADMINS = (("<PERSON>", "<EMAIL>"),)

# https://docs.djangoproject.com/en/5.1/ref/settings/#std:setting-ALLOWED_HOSTS
ALLOWED_HOSTS = list(map(str.strip, os.getenv("ALLOWED_HOSTS", "").split(",")))
CSRF_TRUSTED_ORIGINS = json.loads(os.getenv("DJANGO_CSRF_TRUSTED_ORIGINS", "[]"))

# Application definitions
INSTALLED_APPS = [
    "whitenoise.runserver_nostatic",
    "admin_interface",
    "colorfield",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_extensions",
    "configurator",
]

# Django Admin: use modals instead of popup windows
X_FRAME_OPTIONS = "SAMEORIGIN"

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

if ENV == "development":
    INTERNAL_IPS = ["127.0.0.1"]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "django.template.context_processors.static",
                "django.template.context_processors.media",
                "django.template.context_processors.tz",
            ],
            "loaders": (
                [
                    (
                        "django.template.loaders.cached.Loader",
                        [
                            "configurator.app_template_loader.Loader",
                            "django.template.loaders.filesystem.Loader",
                            "django.template.loaders.app_directories.Loader",
                        ],
                    )
                ]
            ),
            "debug": DEBUG,
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"

# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases
DATABASES = {
    "default": {"ENGINE": "django.db.backends.sqlite3", "NAME": os.path.join(BASE_DIR, "db", "app.db")},
}

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field
DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators
# Disabled password validators as the business wants each user to use the port{n}:port{n} username password combination
AUTH_PASSWORD_VALIDATORS = []

SERVER_EMAIL = os.getenv("SERVER_EMAIL")
EMAIL_HOST = os.getenv("EMAIL_HOST")
EMAIL_HOST_USER = os.getenv("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = os.getenv("EMAIL_HOST_PASSWORD")
DEFAULT_FROM_EMAIL = os.getenv("DEFAULT_FROM_EMAIL")
EMAIL_PORT = os.getenv("EMAIL_PORT")

# Sessions
# https://docs.djangoproject.com/en/5.1/ref/settings/#sessions
SESSION_ENGINE = "django.contrib.sessions.backends.signed_cookies"

# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/
LANGUAGE_CODE = "en-us"
TIME_ZONE = "Australia/Brisbane"
USE_I18N = False
USE_L10N = False
USE_TZ = True

# Security settings
# https://docs.djangoproject.com/en/5.1/topics/security/
SECURE_HSTS_SECONDS = 3000  # 5 minutes
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
USE_X_FORWARDED_HOST = True
USE_X_FORWARDED_PORT = True
SESSION_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_SECURE = not DEBUG

MEDIA_ROOT = os.path.join(BASE_DIR, "media")
MEDIA_URL = "/media/"

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/
STATIC_ROOT = os.path.join(BASE_DIR, "static")
STATIC_URL = "/static/"
STATICFILES_DIRS = (os.path.join(BASE_DIR, "configurator/static"),)
STATICFILES_FINDERS = (
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
)

# Logging
# https://docs.djangoproject.com/en/5.1/topics/logging/
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {message}",
            "style": "{",
        },
        "simple": {
            "format": "{levelname} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
        "file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(BASE_DIR, "django.log"),
            "formatter": "verbose",
            "maxBytes": 10 * 1024 * 1024,
            "backupCount": 5,
        },
    },
    "loggers": {
        "configurator": {
            "handlers": ["console", "file"],
            "level": os.getenv("LOG_LEVEL", "WARNING"),
            "propagate": False,
        },
        "django": {
            "handlers": ["console", "file"],
            "level": os.getenv("LOG_LEVEL", "WARNING"),
            "propagate": True,
        },
        "django.request": {
            "handlers": ["file"],
            "level": "INFO",
            "propagate": False,
        },
    },
}


####################
# PACKAGE SETTINGS #
####################

LOG_IGNORE_PATTERNS = json.loads(os.getenv("LOG_IGNORE_PATTERNS", "[]"))

SENTRY_ENABLED = strtobool(os.getenv("SENTRY_ENABLED", "false"))
if SENTRY_ENABLED:
    sentry_sdk.init(
        dsn=os.getenv("SENTRY_DSN"),
        send_default_pii=True,
        environment=ENV,
        traces_sample_rate=1.0,
        release=VERSION,
        _experiments={
            "enable_logs": True,
            "before_send_log": lambda log, hint=None: None if any(s in log["body"] for s in LOG_IGNORE_PATTERNS) else log,
        },
    )


LOGFIRE_ENABLED = strtobool(os.getenv("LOGFIRE_ENABLED", "false"))
if LOGFIRE_ENABLED:
    logfire.configure(
        token=os.getenv("LOGFIRE_TOKEN"),
        service_name="hallite-configurator",
        service_version=VERSION,
        environment=ENV,
    )
    logfire.instrument_django()
    basicConfig(handlers=[logfire.LogfireLoggingHandler()])


################
# APP SETTINGS #
################

# Excel Service Configuration
EXCEL_WORKBOOK_PATH = os.path.join(BASE_DIR, os.getenv("EXCEL_WORKBOOK_PATH"))
EXCEL_WORKBOOK_VISIBLE = strtobool(os.getenv("EXCEL_WORKBOOK_VISIBLE", "false"))


# Session Management Configuration
EXCEL_SESSION_FILES = os.path.join(
    BASE_DIR,
    os.getenv("EXCEL_SESSION_FILES", "sessions"),
)
EXCEL_SESSION_TIMEOUT = int(os.getenv("EXCEL_SESSION_TIMEOUT", "300"))  # seconds
EXCEL_SESSION_LOCK = strtobool(os.getenv("EXCEL_SESSION_LOCK", "false"))  # Use session-level locking instead of operation-level

# Resource Monitoring Configuration
MAX_RAM_PERCENT = int(os.getenv("MAX_RAM_PERCENT", "80"))
MAX_CPU_PERCENT = int(os.getenv("MAX_CPU_PERCENT", "90"))
RESOURCE_MONITORING_INTERVAL = int(os.getenv("RESOURCE_MONITORING_INTERVAL", "300"))  # seconds

# Email Alert Configuration (Mailgun)
MAILGUN_API_KEY = os.getenv("MAILGUN_API_KEY", "")
MAILGUN_DOMAIN = os.getenv("MAILGUN_DOMAIN", "")
ALERT_EMAIL_FROM = os.getenv("ALERT_EMAIL_FROM", "<EMAIL>")
ALERT_EMAIL_TO = os.getenv("ALERT_EMAIL_TO", "<EMAIL>")

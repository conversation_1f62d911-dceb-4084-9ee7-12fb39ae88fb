import os

from waitress import serve

from config.wsgi import application

bind_host = "0.0.0.0"
bind_port = 9090
threads = int(os.getenv("WAITRESS_THREADS", "4"))
timeout = int(os.getenv("WEB_TIMEOUT", "120"))

if __name__ == "__main__":
    print(f"Starting Waitress server on {bind_host}:{bind_port}")
    print(f"Configured threads: {threads}")

    print(f"Timeout: {timeout} seconds")
    print("")
    print("Note: Waitress is single-process (unlike gun<PERSON>'s multi-process)")
    print("Use multiple Waitress instances behind a load balancer for true multi-process")

    serve(
        application,
        host=bind_host,
        port=bind_port,
        threads=threads,
        channel_timeout=timeout,
        url_scheme="http",
        ident="Hallite-Configurator-API",
    )

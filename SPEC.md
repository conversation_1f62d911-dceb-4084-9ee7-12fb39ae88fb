## Project Summary – “Excel-Backed Hallite Seal Estimator”

Sales engineers must quote custom hydraulic seals quickly, but the pricing logic lives in a complex Excel workbook. We’re adding a thin web layer so users enter dimensions in a browser and receive part numbers, materials, and price tiers—without exposing the spreadsheet or requiring Office on their machines.

The Web layer relies on the sheet for populating its form data as replicating logic display rules in the web app runs the risk of getting out of sync with the Workbook as it will be undergoing changes by the business.

The application has a two-tier architecture:

| Tier | Runtime | Role |
|------|---------|------|
| **Web App** | Linux host running Django | Renders the form, performs light validation, forwards requests, and displays results. |
| **Excel API** | Windows host running Python 3.13 (Django Ninja), xlwings, pywinauto | Automates Excel, runs the `runme` macro to calculate pricing, and returns JSON. Runs as a Windows Service under a dedicated user with Office activated. |

The Excel API generates an OpenAPI spec that will be used to generate Pydantic models for the request and response used by both the Excel API and the Django web app.

This is the specification for the Excel API.

## Work that is to be excluded from the spec

Because the app is already scaffolded, repository created, test runner in place, and infrastructure is configured with deployment management in place, there is lot you do not need to cover in this spec.

Only focus only applicaton development tasks for implementing what is defined in this spec.

Exclude:

- Django configuration
- Django ninja configuration
- Testing and deployment configuration
- Docker-related concerns as it does not user Docker


### Technology Stack

- **Backend**: Django 5.2+, Django Ninja for REST API with Python 3.13+
- **Excel Automation**: xlwings for macro execution
- **Database**: SQLite
- **Package Management**: UV for fast Python dependency management
- **Task Runner**: Just (justfile) for development tasks

---

## Network Flow

1. As user changes form (e.g. selects Profile) → POST to Linux Django back-end.
2. Django adds:
   * `X-API-Key`         – shared secret
   * `x-configurator-session-id`– client-generated UUID (persists across calls)
3. Django POSTs to Excel API `/estimate`.
4. Excel API processes request, returns JSON response.
5. Django re-renders the form using htmx and maybe AlpineJs with updated data, part number, part decription, prices, and material breakdown.

## Excel API

Uses Django and Django Ninja for the REST API.

### Key Design Decisions

* **Authentication** - Uses a shared secret API key for authentication (`X-API-Key`).
* **Session and Concurrency** - Each request requires a `x-configurator-session-id` – a client-generated UUID as workbook opens are persistent and use this ID to name the temporary workbook.
* **Automation** - Uses the xlwings library and and only needs to support a Windows envvironment
* **Isolation** – One workbook copy per request; 5-minute max life (or earlier on error).
* **Dynamic validation** – API enforces rules such as `extra` is required when `extratype` appears,” preventing invalid spreadsheet states. API therefore must inspect state of workbook when validating requests.
* **Performance** – Entire request completes < 5 s; macro typically runs in ~2 s.
* **Robustness** – `pywinauto` detects and captures the messsage in VBA error dialogs so they can be returned in the JSON error response.
* **Ops Guard-Rails**  
  * `MAX_RAM_PERCENT` env (default 80 %) and if exceeded, all temp documents are purged.
  * Watchdog emails **<EMAIL>** and purges temp workbooks if RAM > 90 %.  
  * `/health` endpoint opens/closes a temp copy to prove Excel automation is alive.

### REST API

The request response specs below also include the Excel cell references that will need to be written to and read from.

### Schemas:

Profile just has a code:

```json
[
    "HS110", "HS116", "HS117", "HS118", "HS119", "HS120", "HS121", "HS124", "HS125", "HS129",
    "HS130", "HS131", "HS138", "HS139", "HS238",
    "HFL101", "HFL102", "HFL103", "HFL104", "HFL106", "HFL108",
    "HK101", "HK102", "HK103", "HK104", "HK105", "HK106", "HK107", "HK108", "HK109", "HK110",
    "HK116", "HK117", "HK118", "HK119", "HK120", "HK122", "HK123", "HK124", "HK125", "HK126",
    "EP127", "EP138", "HK140", "EP222",
    "HR101", "HR102", "HR103", "HR104", "HR105", "HR106", "HR108", "HR110", "HR111", "HR112",
    "HR115", "HR116", "HR117", "HR201", "HR202",
    "HA101", "HA102", "HA103", "HA104", "HA105", "HA106", "HA107", "HA108", "HA109",
    "HA111", "HA112", "HA113", "HA114", "HA115", "HA116", "HA211", "HA213"
]
```

Measure is one of:

```json
[
  "Metric",
  "Imperial"
]
```

Material must be one of the following material codes:

```json
[
  { "Material": "A200", "Description": "Polyester Fabric" },
  { "Material": "AFLS", "Description": "AFLAS" },
  { "Material": "ALUM", "Description": "Aluminium" },
  { "Material": "AUSC100", "Description": "Austlon C100 Oil Filled" },
  { "Material": "BRAS", "Description": "Brass" },
  { "Material": "EPDF", "Description": "EPDM FDA" },
  { "Material": "EPDM", "Description": "EPDM 85A" },
  { "Material": "EPDW", "Description": "EPDM Water 85A" },
  { "Material": "ERTA", "Description": "Ertalyte" },
  { "Material": "FP85", "Description": "Viton 85A" },
  { "Material": "HNBR", "Description": "HNBR 85A" },
  { "Material": "NB85", "Description": "Nitrile 85A" },
  { "Material": "PA", "Description": "Nylon (PA)" },
  { "Material": "PEEK", "Description": "PEEK" },
  { "Material": "POMD", "Description": "Acetal Diet (Food)" },
  { "Material": "PU57", "Description": "PU 57D, incl PU 55D" },
  { "Material": "PU95", "Description": "PU 95A, Cast>243mm" },
  { "Material": "SF85", "Description": "Silicon 85A FDA" },
  { "Material": "SI85", "Description": "Silicon 85A" },
  { "Material": "TEF15G", "Description": "Teflon 15% Glass" },
  { "Material": "TEF232", "Description": "Teflon 23% 2% Moly" },
  { "Material": "TEF250", "Description": "Teflon 25% Carbon" },
  { "Material": "TEF25G", "Description": "Teflon 25% Glass" },
  { "Material": "TEF600", "Description": "Teflon 60% Bronze" },
  { "Material": "TEFD05", "Description": "Teflon D05" },
  { "Material": "TEFT", "Description": "Teflon Turcite" },
  { "Material": "TEFV", "Description": "Teflon Virgin" },
  { "Material": "TGRA", "Description": "Teflon 25% Graphite" },
  { "Material": "TMB", "Description": "Teflon Moly Bronze" },
  { "Material": "PU57SL", "Description": "PU57DSL" }
]
```

### /estimate endpoint

Request (includes the sheet cells to write to).

```json
{
  "profile":      "<string>",     // str: `profileentry`
  "measure":      "<string>",     // str "Metric" | "Imperial": `measureentry`
  "od":           "<decimal>",  // decimal (≤600, 3 dp): ODentry
  "id":           "<decimal>",  // decimal (≥5,   3 dp): IDentry
  "ch":           "<decimal>",  // decimal (3 dp): CHentry
  "extra":        "<int>",      // int | null: extraentry
  "material1":    "<string>",     // str: MATL1entry
  "material2":    "<string>",     // str: MATL2entry
  "material3":    "<string>",    // str: MATL3entry
  "refresh":      "<boolean>",  // bool (whether to call the `runme` macro)
}
```

Response (always full shchema, any unset cell = null, includes the sheet cells to write to):

```json
{
  "profile_outputs": {
    "profile": "<string>",                  // value from `profileentry`
    "profile_size": "<string>",              // `D6`
    "measure1": {
      "measure": "<string>",                // value from `measureentry`
      "od": "<number>",                     // value from `ODentry`
      "id": "<number>",                     // value from `IDentry`
      "ch": "<number>",                     // value from `CHentry`
      "extra": {
        "key": "<string>",                  // value from `extratype`
        "value": "<integer>"                // value from `extraentry`
      },
      "material1": "<string>",              // value from `MATL1entry`
      "material2": "<string|null>",         // nullable if `Type2` empty
      "material3": "<string|null>"          // nullable if `Type3` empty
    },
    "measure2": {
      "measure": "<string>",                // `D7`
      "od": "<number>",                     // `D8`
      "id": "<number>",                     // `D9`
      "ch": "<number>",                     // `D10`
      "extra": {
        "key": "<string>",                  // `extratype`
        "value": "<integer>"                // `extraentry`
      },
      "material1": {
        "name": "<string>",                // `Type1`
        "value": "<string>"                // `MATL1entry`
      },
      "material2": {
        "name": "<string>",                // `Type2`
        "value": "<string>"                // `MATL2entry`
      },
      "material3": {
        "name": "<string>",                // `Type3`
        "value": "<string>"                // `MATL3entry`
      }
    }
  },

  "part": {
    "code": "<string>",              // `C31`
    "description": "<string>"          // `partdesc`
  },

  "price": {
    "cost":  "<number>",                    // `costpriceevco`
    "abst":  "<number>",                    // `C19`
    "b1":    "<number>",                    // `C20`
    "retail":"<number>"                     // `C21`
  },

  "materials": {
    "material1": {
      "billet_required":           "<string>",  // `billetreqd1`
      "min_manufacturing_time":    "<number>",  // `mctime1`
      "billet_material_required":  "<string>"   // `mmreqd1`
    },
    "material2": {
      "billet_required":           "<string|null>", // `billetreqd2`
      "min_manufacturing_time":    "<number|null>", // `mctime2`
      "billet_material_required":  "<string|null>"  // `mmreqd2`
    },
    "material3": {
      "billet_required":           "<string|null>", // `billetreqd3`
      "min_manufacturing_time":    "<number|null>", // `mctime3`
      "billet_material_required":  "<string|null>"  // `mmreqd3`
    }
  }
}
```

### Session & workbook lifecycle

* Each request uses its own workbook copy; no shared state.
* Unlimited parallel sessions (memory-constrained).
* New UUID ⇒ copy workbook to %TEMP%\{uuid}.xlsm, open with xlwings.
* 5-minute fixed lifespan OR immediate discard on explicit timeout / error.
* All writes follow this order with 200 ms delay between writes:
    profileentry → measureentry → ODentry → IDentry → CHentry
    → extratype? → extraentry? → MATL1entry → MATL2entry? → MATL3entry?
* `refresh=true` ⇒ run `runme` (Application.DisplayAlerts=False);
  macro + read phase must finish ≤ 5 s total.
* On timeout: close Excel, delete temp copy, return 503.
* Every request checks for temp files >5 min old and deletes them.

## Interface between REST API and Excel workbook

I'm thinking a Service that represents the state, inputs, and outputs of the Excel document is how the REST API will interrogate the state of the workbook for request validation and to read the output.

There can also be an `Excel` or similar service that handles the low-level interaction with Excel, opening/closing workbooks, reading/writing values, and executing macros.

This should create a clear separation and make testing easier.

### Resource guardrails

* Env var `MAX_RAM_PERCENT` (default 90 %):
* Watchdog samples RAM every 30 s; if > 90 %:
    – Email alert via <NAME_EMAIL> to
      <EMAIL> with subject
      “api high-memory alert”.
    – Terminate all Excel processes, purge %TEMP%\*.xlsm copies.
    – Service never denies requests

### Health Check

GET /health-check  (no auth)
* Creates a temp copy of the workbook (using same function as API, but creating a dummy session-id), opens, closes it, deletes it, then returns
  { "status": "ok" } 200.

### OpenAPI and models

• Shared OpenAPI spec generated by Linux Django Ninja app.
• Pydantic request/response models pre-generated from that spec and
  vendored into the Windows service repo.

### settings.py config variables from environment variables

WORKBOOK_PATH            – absolute path to master .xlsm
KEEPALIVE_MINUTES        – 5  (ignored: session is temp per request)
MAX_RAM_PERCENT          – 80
LOG_LEVEL                – default INFO
TMP_DIR                  – defaults to %TEMP%

### Workbook Error Handling

The Excel API inspects key cells **after** writing inputs (and, if applicable, after
running the `runme` macro) to determine whether the workbook signalled an error
or requires a forced recalculation.

| Condition (cell value)                                | API behaviour                                                                                          |
|-------------------------------------------------------|--------------------------------------------------------------------------------------------------------|
| `costpriceevco` **== 0**                              | Treat as a spreadsheet-level error. Read the human-readable message in `partdesc` and return:<br>`HTTP 422` → `{ "type": "sheet_error", "msg": "<partdesc text>" }` |
| `partdesc` **== "Material/Size not available"**       | Return `HTTP 422` with `{ "type": "sheet_error", "msg": "Material/Size not available" }`.              |
| `partdesc` **== "ID too small. Check with Technical."`| Return `HTTP 422` with `{ "type": "sheet_error", "msg": "ID too small. Check with Technical." }`.       |
| `partdesc` **== "OD TOO LARGE"`                       | Return `HTTP 422` with `{ "type": "sheet_error", "msg": "OD TOO LARGE" }`.                             |
| `costpriceevco` **== "refresh data"**                 | Spreadsheet requests a recalculation. The API automatically executes the `runme` macro once, then rereads all output cells and returns the normal `200 OK` response. If the subsequent pass still sets `costpriceevco` to `"refresh data"`, the API returns `HTTP 500` with `{ "type": "sheet_error", "msg": "Repeated refresh request" }`. |

For VB runtime errors, `pywinauto` monitors for VB runtime dialogs titled “*Visual Basic*”.
  – Captures text, clicks “End”, raises Python COMError.
  – Converted to JSON { "type": "COMError", "msg": "Run-time error '9': …" }.

**Notes**

* Error comparisons are **case-sensitive** to match exact workbook text.  
* The `"sheet_error"` 422 payload is designed so the Web App can surface the message directly to the user without exposing spreadsheet terminology.  
* No other output fields are included in 422 responses.  
* The automatic `runme` retry for `"refresh data"` is silent to the client; it merely causes a slightly longer response time (still bounded by the 5-second overall timeout).  

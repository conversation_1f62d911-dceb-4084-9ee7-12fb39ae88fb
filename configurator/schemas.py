from typing import Optional

from django.contrib.auth import get_user_model
from ninja import ModelSchema
from pydantic import BaseModel, field_validator

from .constants import MATERIALS, PROFILES

User = get_user_model()


class UserSchema(ModelSchema):
    class Meta:
        model = User
        fields = ["id", "username", "first_name", "last_name", "email"]


class WorkbookInputs(BaseModel):
    """Domain model representing inputs to the Excel configurator workbook."""

    profile: str
    measure: str
    od: float
    id: float
    ch: float
    extra: Optional[int] = None
    material1: str
    material2: Optional[str] = None
    material3: Optional[str] = None

    @field_validator("id")
    @classmethod
    def validate_id(cls, v: float) -> float:
        if v + 2 < 3:
            raise ValueError(f"Invalid id: {v}. {v} + 2 must be >= 3.")
        return v

    @field_validator("od")
    @classmethod
    def validate_od(cls, v: float) -> float:
        if v > 600:
            raise ValueError(f"Invalid od: {v}. Must be <= 600.")
        return v

    @field_validator("profile")
    @classmethod
    def validate_profile(cls, v: str) -> str:
        if v not in PROFILES:
            raise ValueError(f"Invalid profile: {v}. Must be one of the valid profiles.")
        return v

    @field_validator("material1")
    @classmethod
    def validate_material1(cls, v: str) -> str:
        if v not in MATERIALS:
            raise ValueError(f"Invalid material1: {v}. Must be one of the valid materials.")
        return v

    @field_validator("material2")
    @classmethod
    def validate_material2(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and v not in MATERIALS:
            raise ValueError(f"Invalid material2: {v}. Must be one of the valid materials.")
        return v

    @field_validator("material3")
    @classmethod
    def validate_material3(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and v not in MATERIALS:
            raise ValueError(f"Invalid material3: {v}. Must be one of the valid materials.")
        return v


class ExtraInfo(BaseModel):
    """Extra information with key-value pair."""

    key: str
    value: int


class MaterialInfo(BaseModel):
    """Material information with name and value."""

    name: str
    value: str


class Measure(BaseModel):
    """Measure section of profile outputs containing core measurement data."""

    measure: str
    od: float
    id: float
    ch: float


class ProfileOutputs(BaseModel):
    """Profile outputs section containing all profile-related data."""

    profile: str
    profile_size: str
    measure: Measure
    extra: Optional[ExtraInfo] = None
    material1: MaterialInfo
    material2: Optional[MaterialInfo] = None
    material3: Optional[MaterialInfo] = None


class ProfileInfo(BaseModel):
    """Profile information for dynamic form rendering."""

    profile: str
    profile_size: str
    extra: Optional[str] = None
    material1: str
    material2: Optional[str] = None
    material3: Optional[str] = None


class Part(BaseModel):
    """Part information section."""

    code: str
    description: str


class Price(BaseModel):
    """Pricing information section."""

    cost: str
    abst: str
    b1: str
    retail: str


class MaterialDetails(BaseModel):
    """Material details for manufacturing."""

    billet_required: str
    min_manufacturing_time: float
    billet_material_required: str


class Materials(BaseModel):
    """Materials section containing all material details."""

    material1: MaterialDetails
    material2: Optional[MaterialDetails] = None
    material3: Optional[MaterialDetails] = None


class WorkbookOutputs(BaseModel):
    """Domain model representing outputs from the Excel configurator workbook."""

    profile_outputs: ProfileOutputs
    part: Part
    price: Price
    materials: Materials


# API Schemas for REST endpoints


class EstimateRequest(WorkbookInputs):
    """API request schema for /estimate endpoint."""


class EstimateResponse(WorkbookOutputs):
    """API response schema for /estimate endpoint."""


class SheetErrorResponse(BaseModel):
    """Error response for sheet-level errors."""

    type: str = "sheet_error"
    msg: str

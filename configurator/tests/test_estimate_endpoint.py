# type: ignore

import uuid
from unittest.mock import Mock, patch

from django.test import TestCase
from ninja.testing import TestClient

from configurator.router import router
from configurator.schemas import WorkbookOutputs


class EstimateEndpointTest(TestCase):

    def setUp(self):
        self.client = TestClient(router)
        self.session_id = str(uuid.uuid4())
        self.valid_request_data = {
            "profile": "HK109",
            "measure": "Metric",
            "od": "100.000",
            "id": "80.000",
            "ch": "10.000",
            "material1": "NB85",
        }

    def test_missing_session_id_returns_400(self):
        response = self.client.post("/estimate", json=self.valid_request_data)
        self.assertEqual(response.status_code, 400)
        self.assertIn("Missing x-configurator-session-id header", response.json()["detail"])

    def test_invalid_session_id_returns_400(self):
        response = self.client.post("/estimate", json=self.valid_request_data, headers={"x-configurator-session-id": "invalid-uuid"})
        self.assertEqual(response.status_code, 400)
        self.assertIn("Invalid x-configurator-session-id format", response.json()["detail"])

    @patch("configurator.router.WorkbookSessionService")
    def test_successful_estimate_returns_200(self, mock_session_service_class):
        # Create a minimal valid WorkbookOutputs structure
        mock_outputs_data = {
            "profile_outputs": {
                "profile": "HK109",
                "profile_size": "REF123",
                "measure": {
                    "measure": "Metric",
                    "od": 100.0,
                    "id": 80.0,
                    "ch": 10.0,
                },
                "extra": {"key": "test", "value": 1},
                "material1": {"name": "Type1", "value": "NB85"},
                "material2": {"name": "Type2", "value": ""},
                "material3": {"name": "Type3", "value": ""},
            },
            "part": {"code": "PART123", "description": "Test Part"},
            "price": {"cost": "100.0", "abst": "110.0", "b1": "120.0", "retail": "130.0"},
            "materials": {
                "material1": {"billet_required": "Yes", "min_manufacturing_time": 5.0, "billet_material_required": "Steel"},
                "material2": None,
                "material3": None,
            },
        }

        # Create a proper WorkbookOutputs instance
        mock_outputs = WorkbookOutputs(**mock_outputs_data)

        mock_workbook_service = Mock()
        mock_workbook_service.process.return_value = (mock_outputs, None)
        # Add context manager support
        mock_workbook_service.__enter__ = Mock(return_value=mock_workbook_service)
        mock_workbook_service.__exit__ = Mock(return_value=None)

        mock_session_service = Mock()
        mock_session_service.open_workbook_session.return_value = mock_workbook_service
        mock_session_service_class.get_service.return_value = mock_session_service

        response = self.client.post("/estimate", json=self.valid_request_data, headers={"x-configurator-session-id": self.session_id})

        self.assertEqual(response.status_code, 200)
        mock_session_service.open_workbook_session.assert_called_once_with(self.session_id)

    @patch("configurator.router.WorkbookSessionService")
    def test_sheet_error_returns_422(self, mock_session_service_class):
        mock_workbook_service = Mock()
        mock_workbook_service.process.return_value = (None, "Material/Size not available")
        # Add context manager support
        mock_workbook_service.__enter__ = Mock(return_value=mock_workbook_service)
        mock_workbook_service.__exit__ = Mock(return_value=None)

        mock_session_service = Mock()
        mock_session_service.open_workbook_session.return_value = mock_workbook_service
        mock_session_service_class.get_service.return_value = mock_session_service

        response = self.client.post("/estimate", json=self.valid_request_data, headers={"x-configurator-session-id": self.session_id})

        self.assertEqual(response.status_code, 422)
        error_data = response.json()
        self.assertIn("detail", error_data)
        self.assertEqual(len(error_data["detail"]), 1)
        error = error_data["detail"][0]
        self.assertEqual(error["type"], "sheet_error")
        self.assertEqual(error["msg"], "Material/Size not available")

    def test_invalid_request_data_returns_422(self):
        invalid_data = self.valid_request_data.copy()
        invalid_data["od"] = "700.000"  # Exceeds maximum

        response = self.client.post("/estimate", json=invalid_data, headers={"x-configurator-session-id": self.session_id})

        self.assertEqual(response.status_code, 422)
        error_data = response.json()
        self.assertIn("detail", error_data)
        # Verify the validation error is about the OD field
        errors = error_data["detail"]
        od_errors = [e for e in errors if "od" in str(e).lower()]
        self.assertTrue(od_errors, "Expected validation error for 'od' field")

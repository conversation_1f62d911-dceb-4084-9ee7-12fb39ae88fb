# type: ignore
# pylint: disable=protected-access

"""
Tests for WorkbookSessionService.

These tests cover the minimum required functionality for session management:
- Session creation and unique workbook copies
- Session cleanup
- Expired session cleanup (5-minute lifespan)
- Singleton pattern
- Error handling for missing master workbook
"""

import tempfile
import threading
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch

from django.test import SimpleTestCase

from configurator.services import (
    ExcelService,
    WorkbookService,
    WorkbookSessionError,
    WorkbookSessionService,
)


class WorkbookSessionServiceTest(SimpleTestCase):
    """Tests for WorkbookSessionService."""

    def setUp(self):
        """Set up test fixtures."""
        # Reset singleton instance for each test
        WorkbookSessionService._instance = None

        # Create a temporary directory and master workbook file for testing
        self.temp_dir = tempfile.TemporaryDirectory()
        self.temp_dir_path = Path(self.temp_dir.name)
        self.master_workbook_path = self.temp_dir_path / "master.xlsm"
        self.master_workbook_path.touch()  # Create empty file

    def tearDown(self):
        """Clean up test fixtures."""
        self.temp_dir.cleanup()
        WorkbookSessionService._instance = None

    def test_session_creation_and_unique_workbook_copies(self):
        """Test that open_workbook_session creates unique temporary workbook copies with session_id naming."""
        session_id = "test-session-123"

        # Mock ExcelService.open to avoid actual Excel operations
        mock_excel_service = Mock(spec=ExcelService)

        # Create a mock WorkbookService that supports context manager protocol
        mock_workbook_service = Mock(spec=WorkbookService)
        mock_workbook_service.__enter__ = Mock(return_value=mock_workbook_service)
        mock_workbook_service.__exit__ = Mock(return_value=None)

        with (
            patch("configurator.services.workbook_session.ExcelService.open", return_value=mock_excel_service),
            patch("configurator.services.workbook_session.shutil.copy2") as mock_copy,
            patch("configurator.services.workbook_session.WorkbookService", return_value=mock_workbook_service),
        ):

            # Create session manager
            manager = WorkbookSessionService(str(self.master_workbook_path), str(self.temp_dir_path), session_timeout=300)

            # Open workbook for session
            result = manager.open_workbook_session(session_id)

            # Verify workbook copy was created with correct naming
            expected_session_path = self.temp_dir_path / f"{session_id}.xlsm"
            mock_copy.assert_called_once_with(self.master_workbook_path, expected_session_path)

            # Verify WorkbookService is returned
            self.assertEqual(result, mock_workbook_service)

            # Verify session access time is tracked (new thread-safe approach)
            self.assertIn(session_id, manager._sessions_access)

    def test_session_reuse_creates_fresh_excel_objects(self):
        """Test that opening the same session_id creates fresh Excel objects for thread safety."""
        session_id = "test-session-456"

        # Mock ExcelService
        mock_excel_service1 = Mock(spec=ExcelService)
        mock_excel_service2 = Mock(spec=ExcelService)

        # Create mock WorkbookService instances that support context manager protocol
        mock_workbook_service1 = Mock(spec=WorkbookService)
        mock_workbook_service1.__enter__ = Mock(return_value=mock_workbook_service1)
        mock_workbook_service1.__exit__ = Mock(return_value=None)
        mock_workbook_service1.excel_service = mock_excel_service1

        mock_workbook_service2 = Mock(spec=WorkbookService)
        mock_workbook_service2.__enter__ = Mock(return_value=mock_workbook_service2)
        mock_workbook_service2.__exit__ = Mock(return_value=None)
        mock_workbook_service2.excel_service = mock_excel_service2

        with (
            patch("configurator.services.workbook_session.ExcelService.open") as mock_open,
            patch("configurator.services.workbook_session.shutil.copy2"),
            patch("configurator.services.workbook_session.WorkbookService") as mock_workbook_class,
        ):
            # Configure mock to return different services (thread-safe approach)
            mock_open.side_effect = [mock_excel_service1, mock_excel_service2]
            mock_workbook_class.side_effect = [mock_workbook_service1, mock_workbook_service2]

            manager = WorkbookSessionService(str(self.master_workbook_path), str(self.temp_dir_path), session_timeout=300)

            # Open workbook twice with same session_id
            result1 = manager.open_workbook_session(session_id)
            result2 = manager.open_workbook_session(session_id)

            # Verify both results are the expected mock WorkbookService instances
            self.assertEqual(result1, mock_workbook_service1)
            self.assertEqual(result2, mock_workbook_service2)

            # Verify different Excel services are used (thread-safe approach)
            # This is the key improvement - fresh objects each time for thread safety
            self.assertNotEqual(result1.excel_service, result2.excel_service)
            self.assertEqual(result1.excel_service, mock_excel_service1)
            self.assertEqual(result2.excel_service, mock_excel_service2)

            # Verify both calls to ExcelService.open were made
            self.assertEqual(mock_open.call_count, 2)

    def test_error_handling_for_missing_master_workbook(self):
        """Test that constructor raises WorkbookSessionError when master workbook doesn't exist."""
        non_existent_path = "/path/that/does/not/exist.xlsm"

        with self.assertRaises(WorkbookSessionError) as context:
            WorkbookSessionService(non_existent_path, str(self.temp_dir_path), session_timeout=300)

        self.assertIn("Master workbook not found", str(context.exception))
        self.assertIn(non_existent_path, str(context.exception))

    def test_session_cleanup_successful(self):
        """Test that cleanup_session properly deletes temporary file and removes tracking."""
        session_id = "test-session-cleanup"

        # Mock ExcelService and file operations
        mock_excel_service = Mock(spec=ExcelService)

        # Create a mock WorkbookService that supports context manager protocol
        mock_workbook_service = Mock(spec=WorkbookService)
        mock_workbook_service.__enter__ = Mock(return_value=mock_workbook_service)
        mock_workbook_service.__exit__ = Mock(return_value=None)

        with (
            patch("configurator.services.workbook_session.ExcelService.open", return_value=mock_excel_service),
            patch("configurator.services.workbook_session.shutil.copy2"),
            patch("configurator.services.workbook_session.WorkbookService", return_value=mock_workbook_service),
            patch("pathlib.Path.exists", return_value=True),
            patch("pathlib.Path.unlink") as mock_unlink,
        ):

            manager = WorkbookSessionService(str(self.master_workbook_path), str(self.temp_dir_path), session_timeout=300)

            # Create a session first
            manager.open_workbook_session(session_id)

            # Verify session access time is tracked
            self.assertIn(session_id, manager._sessions_access)

            # Clean up the session
            result = manager.cleanup_session(session_id)

            # Verify cleanup was successful
            self.assertTrue(result)
            # Verify file deletion was attempted
            self.assertGreaterEqual(mock_unlink.call_count, 1)

            # Verify session is removed from access tracking
            self.assertNotIn(session_id, manager._sessions_access)

    def test_session_cleanup_non_existent_session(self):
        """Test that cleanup_session handles non-existent sessions gracefully."""
        manager = WorkbookSessionService(str(self.master_workbook_path), str(self.temp_dir_path), session_timeout=300)

        # Try to clean up a session that doesn't exist
        result = manager.cleanup_session("non-existent-session")

        # Should return False for non-existent session
        self.assertFalse(result)

    def test_expired_session_cleanup_5_minute_lifespan(self):
        """Test that cleanup_expired_sessions removes sessions older than 5 minutes automatically."""
        session_id_old = "old-session"
        session_id_new = "new-session"

        # Mock ExcelService
        mock_excel_service_old = Mock(spec=ExcelService)
        mock_excel_service_new = Mock(spec=ExcelService)

        # Create mock WorkbookService instances that support context manager protocol
        mock_workbook_service_old = Mock(spec=WorkbookService)
        mock_workbook_service_old.__enter__ = Mock(return_value=mock_workbook_service_old)
        mock_workbook_service_old.__exit__ = Mock(return_value=None)

        mock_workbook_service_new = Mock(spec=WorkbookService)
        mock_workbook_service_new.__enter__ = Mock(return_value=mock_workbook_service_new)
        mock_workbook_service_new.__exit__ = Mock(return_value=None)

        # Create time references
        current_time = datetime.now()
        old_time = current_time - timedelta(minutes=6)  # 6 minutes ago (expired)
        new_time = current_time - timedelta(minutes=2)  # 2 minutes ago (not expired)

        with (
            patch("configurator.services.workbook_session.ExcelService.open") as mock_open,
            patch("configurator.services.workbook_session.shutil.copy2"),
            patch("configurator.services.workbook_session.WorkbookService") as mock_workbook_class,
            patch("pathlib.Path.exists", return_value=True),
            patch("pathlib.Path.unlink"),
        ):

            # Configure mock to return different services for different sessions
            mock_open.side_effect = [mock_excel_service_old, mock_excel_service_new]
            mock_workbook_class.side_effect = [mock_workbook_service_old, mock_workbook_service_new]

            manager = WorkbookSessionService(str(self.master_workbook_path), str(self.temp_dir_path), session_timeout=300)

            # Create sessions with different timestamps
            manager.open_workbook_session(session_id_old)
            manager.open_workbook_session(session_id_new)

            # Manually set the access times to simulate age
            manager._sessions_access[session_id_old] = old_time
            manager._sessions_access[session_id_new] = new_time

            # Verify both sessions exist in access tracking before cleanup
            self.assertIn(session_id_old, manager._sessions_access)
            self.assertIn(session_id_new, manager._sessions_access)

            # Mock datetime.now() only for the cleanup check
            with patch("configurator.services.workbook_session.datetime") as mock_datetime:
                mock_datetime.now.return_value = current_time

                # Run expired session cleanup
                expired_count = manager.cleanup_expired_sessions()

                # Verify only the old session was cleaned up
                self.assertEqual(expired_count, 1)
                self.assertNotIn(session_id_old, manager._sessions_access)
                self.assertIn(session_id_new, manager._sessions_access)

    def test_singleton_pattern(self):
        """Test that get_service() returns the same instance on multiple calls."""
        # Mock settings to avoid dependency on actual settings
        with patch("configurator.services.workbook_session.settings") as mock_settings:
            mock_settings.EXCEL_WORKBOOK_PATH = str(self.master_workbook_path)
            mock_settings.EXCEL_SESSION_FILES = str(self.temp_dir_path)
            mock_settings.EXCEL_SESSION_TIMEOUT = 300

            # Reset singleton instance
            WorkbookSessionService._instance = None

            # Get service instances
            service1 = WorkbookSessionService.get_service()
            service2 = WorkbookSessionService.get_service()

            # Verify they are the same instance
            self.assertIs(service1, service2)
            self.assertIsInstance(service1, WorkbookSessionService)
            self.assertIsInstance(service2, WorkbookSessionService)

    def test_singleton_thread_safety(self):
        """Test that singleton pattern works correctly with concurrent access."""

        # Mock settings
        with patch("configurator.services.workbook_session.settings") as mock_settings:
            mock_settings.EXCEL_WORKBOOK_PATH = str(self.master_workbook_path)
            mock_settings.EXCEL_SESSION_FILES = str(self.temp_dir_path)
            mock_settings.EXCEL_SESSION_TIMEOUT = 300

            # Reset singleton instance
            WorkbookSessionService._instance = None

            instances = []

            def get_service_instance():
                instance = WorkbookSessionService.get_service()
                instances.append(instance)

            # Create multiple threads that try to get the service
            threads = []
            for _ in range(5):
                thread = threading.Thread(target=get_service_instance)
                threads.append(thread)

            # Start all threads
            for thread in threads:
                thread.start()

            # Wait for all threads to complete
            for thread in threads:
                thread.join()

            # Verify all threads got the same instance
            self.assertEqual(len(instances), 5)
            for instance in instances:
                self.assertIs(instance, instances[0])

# type: ignore

"""
Tests for configurator input validation using constants.
"""

import pytest
from pydantic import ValidationError

from configurator.schemas import WorkbookInputs


class TestConfiguratorWorkbookInputsValidation:
    """Test validation of ConfiguratorWorkbookInputs using constants."""

    def test_valid_inputs_pass_validation(self):
        """Test that valid inputs pass validation."""
        valid_inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            material2="TEFV",
            material3="PU95",
        )

        assert valid_inputs.profile == "HS110"
        assert valid_inputs.material1 == "EPDM"
        assert valid_inputs.material2 == "TEFV"
        assert valid_inputs.material3 == "PU95"

    def test_invalid_profile_raises_validation_error(self):
        """Test that invalid profile raises ValidationError."""
        with pytest.raises(ValidationError) as exc_info:
            WorkbookInputs(
                profile="INVALID_PROFILE",
                measure="metric",
                od=float("100.0"),
                id=float("80.0"),
                ch=float("10.0"),
                material1="EPDM",
            )

        assert "Invalid profile: INVALID_PROFILE" in str(exc_info.value)

    def test_invalid_material1_raises_validation_error(self):
        """Test that invalid material1 raises ValidationError."""
        with pytest.raises(ValidationError) as exc_info:
            WorkbookInputs(
                profile="HS110",
                measure="metric",
                od=float("100.0"),
                id=float("80.0"),
                ch=float("10.0"),
                material1="INVALID_MATERIAL",
            )

        assert "Invalid material1: INVALID_MATERIAL" in str(exc_info.value)

    def test_invalid_material2_raises_validation_error(self):
        """Test that invalid material2 raises ValidationError."""
        with pytest.raises(ValidationError) as exc_info:
            WorkbookInputs(
                profile="HS110",
                measure="metric",
                od=float("100.0"),
                id=float("80.0"),
                ch=float("10.0"),
                material1="EPDM",
                material2="INVALID_MATERIAL",
            )

        assert "Invalid material2: INVALID_MATERIAL" in str(exc_info.value)

    def test_invalid_material3_raises_validation_error(self):
        """Test that invalid material3 raises ValidationError."""
        with pytest.raises(ValidationError) as exc_info:
            WorkbookInputs(
                profile="HS110",
                measure="metric",
                od=float("100.0"),
                id=float("80.0"),
                ch=float("10.0"),
                material1="EPDM",
                material3="INVALID_MATERIAL",
            )

        assert "Invalid material3: INVALID_MATERIAL" in str(exc_info.value)

    def test_none_materials_are_valid(self):
        """Test that None values for optional materials are valid."""
        valid_inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            material2=None,
            material3=None,
        )

        assert valid_inputs.material2 is None
        assert valid_inputs.material3 is None

    def test_id_validation_minimum_value(self):
        """Test that id + 2 must be >= 3 (i.e., id >= 1.0)."""
        # Valid id at minimum boundary
        valid_inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("1.0"),  # Minimum valid value (id + 2 = 3)
            ch=float("10.0"),
            material1="EPDM",
        )
        assert valid_inputs.id == float("1.0")

        # Invalid id below minimum
        with pytest.raises(ValidationError) as exc_info:
            WorkbookInputs(
                profile="HS110",
                measure="metric",
                od=float("100.0"),
                id=float("0.9"),  # Below minimum
                ch=float("10.0"),
                material1="EPDM",
            )
        assert "Invalid id: 0.9. 0.9 + 2 must be >= 3" in str(exc_info.value)

    def test_od_validation_maximum_value(self):
        """Test that od must be <= 600."""
        # Valid od at maximum boundary
        valid_inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("600.0"),  # Maximum valid value
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
        )
        assert valid_inputs.od == float("600.0")

        # Invalid od above maximum
        with pytest.raises(ValidationError) as exc_info:
            WorkbookInputs(
                profile="HS110",
                measure="metric",
                od=float("600.1"),  # Above maximum
                id=float("80.0"),
                ch=float("10.0"),
                material1="EPDM",
            )
        assert "Invalid od: 600.1. Must be <= 600." in str(exc_info.value)

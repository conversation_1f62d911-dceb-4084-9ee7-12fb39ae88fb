"""
Tests for cleanup management commands.

These tests verify that the cleanup commands properly report their actions
and handle various scenarios correctly.
"""

from unittest.mock import Mock, patch

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.test import SimpleTestCase

from configurator.management.commands.cleanup_excel_processes import command as cleanup_processes_command
from configurator.management.commands.cleanup_excel_sessions import command as cleanup_sessions_command


class CleanupCommandsTest(SimpleTestCase):
    """Tests for cleanup management commands."""

    @patch("configurator.management.commands.cleanup_excel_sessions.WorkbookSessionService")
    def test_cleanup_excel_sessions_with_items_to_clean(self, mock_service_class):
        """Test cleanup_excel_sessions command when there are items to clean up."""
        # Mock the service instance
        mock_service = Mock()
        mock_service.cleanup_all_sessions.return_value = 2
        mock_service.cleanup_excel_lock_files.return_value = 1
        mock_service.cleanup_excel_processes.return_value = 3
        mock_service_class.get_service.return_value = mock_service

        # Use <PERSON><PERSON>'s test runner
        runner = <PERSON>liRunner()
        result = runner.invoke(cleanup_sessions_command)

        # Verify all cleanup methods were called
        mock_service.cleanup_all_sessions.assert_called_once()
        mock_service.cleanup_excel_lock_files.assert_called_once()
        mock_service.cleanup_excel_processes.assert_called_once()

        # Verify output contains expected messages
        self.assertIn("✅ Cleaned up 2 sessions and orphaned files", result.output)
        self.assertIn("✅ Cleaned up 1 Excel lock files", result.output)
        self.assertIn("✅ Killed 3 Excel process(es)", result.output)
        self.assertIn("Successfully completed comprehensive cleanup", result.output)

    @patch("configurator.management.commands.cleanup_excel_sessions.WorkbookSessionService")
    def test_cleanup_excel_sessions_with_nothing_to_clean(self, mock_service_class):
        """Test cleanup_excel_sessions command when there's nothing to clean up."""
        # Mock the service instance to return zero for all cleanup operations
        mock_service = Mock()
        mock_service.cleanup_all_sessions.return_value = 0
        mock_service.cleanup_excel_lock_files.return_value = 0
        mock_service.cleanup_excel_processes.return_value = 0
        mock_service_class.get_service.return_value = mock_service

        # Use Click's test runner
        runner = CliRunner()
        result = runner.invoke(cleanup_sessions_command)

        # Verify all cleanup methods were called
        mock_service.cleanup_all_sessions.assert_called_once()
        mock_service.cleanup_excel_lock_files.assert_called_once()
        mock_service.cleanup_excel_processes.assert_called_once()

        # Verify output contains expected message for no cleanup
        self.assertIn("No sessions, files, or processes found to clean up", result.output)
        # Verify individual success messages are NOT present when counts are 0
        self.assertNotIn("✅ Cleaned up", result.output)
        self.assertNotIn("✅ Killed", result.output)

    @patch("configurator.management.commands.cleanup_excel_sessions.WorkbookSessionService")
    def test_cleanup_excel_sessions_partial_cleanup(self, mock_service_class):
        """Test cleanup_excel_sessions command with partial cleanup (some items found)."""
        # Mock the service instance with mixed results
        mock_service = Mock()
        mock_service.cleanup_all_sessions.return_value = 0  # No sessions
        mock_service.cleanup_excel_lock_files.return_value = 2  # Some lock files
        mock_service.cleanup_excel_processes.return_value = 0  # No processes
        mock_service_class.get_service.return_value = mock_service

        # Use Click's test runner
        runner = CliRunner()
        result = runner.invoke(cleanup_sessions_command)

        # Verify output contains only the relevant success message
        self.assertNotIn("✅ Cleaned up 0 sessions", result.output)  # Should not show 0 counts
        self.assertIn("✅ Cleaned up 2 Excel lock files", result.output)  # Should show non-zero counts
        self.assertNotIn("✅ Killed 0 Excel", result.output)  # Should not show 0 counts
        self.assertIn("Successfully completed comprehensive cleanup", result.output)  # Total > 0

    @patch("configurator.management.commands.cleanup_excel_processes.WorkbookSessionService")
    def test_cleanup_excel_processes_with_processes(self, mock_service_class):
        """Test cleanup_excel_processes command when Excel processes are found."""
        # Mock the service instance
        mock_service = Mock()
        mock_service.cleanup_excel_processes.return_value = 2
        mock_service_class.get_service.return_value = mock_service

        # Use Click's test runner
        runner = CliRunner()
        result = runner.invoke(cleanup_processes_command)

        # Verify cleanup method was called
        mock_service.cleanup_excel_processes.assert_called_once()

        # Verify output contains expected message
        self.assertIn("Successfully killed 2 Excel process(es)", result.output)

    @patch("configurator.management.commands.cleanup_excel_processes.WorkbookSessionService")
    def test_cleanup_excel_processes_no_processes(self, mock_service_class):
        """Test cleanup_excel_processes command when no Excel processes are found."""
        # Mock the service instance
        mock_service = Mock()
        mock_service.cleanup_excel_processes.return_value = 0
        mock_service_class.get_service.return_value = mock_service

        # Use Click's test runner
        runner = CliRunner()
        result = runner.invoke(cleanup_processes_command)

        # Verify cleanup method was called
        mock_service.cleanup_excel_processes.assert_called_once()

        # Verify output contains expected message
        self.assertIn("No Excel processes found to kill", result.output)

    @patch("configurator.management.commands.cleanup_excel_sessions.WorkbookSessionService")
    def test_cleanup_all_sessions_accurate_counting(self, mock_service_class):
        """Test that cleanup_all_sessions only counts successfully cleaned sessions."""
        # Mock the service instance
        mock_service = Mock()

        # Simulate scenario where some sessions fail to clean up
        mock_service.cleanup_all_sessions.return_value = 2  # Only 2 out of 4 sessions successfully cleaned
        mock_service.cleanup_excel_lock_files.return_value = 1
        mock_service.cleanup_excel_processes.return_value = 0
        mock_service_class.get_service.return_value = mock_service

        # Use Click's test runner
        runner = CliRunner()
        result = runner.invoke(cleanup_sessions_command)

        # Verify that the command reports the accurate counts
        self.assertIn("✅ Cleaned up 2 sessions and orphaned files", result.output)
        self.assertIn("✅ Cleaned up 1 Excel lock files", result.output)
        self.assertNotIn("✅ Killed", result.output)  # No processes killed
        self.assertIn("Successfully completed comprehensive cleanup", result.output)

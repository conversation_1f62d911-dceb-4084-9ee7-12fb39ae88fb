# type: ignore

import uuid
from unittest.mock import Mock, patch

from django.contrib.auth import get_user_model
from django.test import TestCase
from ninja.testing import TestClient

from configurator.router import router

User = get_user_model()


class EndSessionEndpointTest(TestCase):

    def setUp(self):
        self.client = TestClient(router)
        self.session_id = str(uuid.uuid4())
        # Create a test user for authentication
        self.user = User.objects.create_user(username="testuser", email="<EMAIL>", password="testpass")

    @patch("configurator.api.APIKeyAuth.authenticate")
    def test_missing_session_id_returns_400(self, mock_auth):
        # Mock authentication to return the test user
        mock_auth.return_value = self.user

        response = self.client.delete("/end-session")
        self.assertEqual(response.status_code, 400)
        self.assertIn("Missing x-configurator-session-id header", response.json()["detail"])

    @patch("configurator.api.APIKeyAuth.authenticate")
    def test_invalid_session_id_returns_400(self, mock_auth):
        # Mock authentication to return the test user
        mock_auth.return_value = self.user

        response = self.client.delete("/end-session", headers={"x-configurator-session-id": "invalid-uuid"})
        self.assertEqual(response.status_code, 400)
        self.assertIn("Invalid x-configurator-session-id format", response.json()["detail"])

    @patch("configurator.api.APIKeyAuth.authenticate")
    @patch("configurator.router.WorkbookSessionService")
    def test_successful_session_cleanup(self, mock_session_service_class, mock_auth):
        # Mock authentication to return the test user
        mock_auth.return_value = self.user

        # Mock WorkbookSessionService
        mock_session_service = Mock()
        mock_session_service.force_close_session.return_value = True
        mock_session_service_class.get_service.return_value = mock_session_service

        response = self.client.delete("/end-session", headers={"x-configurator-session-id": self.session_id})

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data["status"], "success")
        self.assertEqual(response_data["message"], "Session ended and Excel workbook closed")

        # Verify force_close_session was called with correct session_id
        mock_session_service.force_close_session.assert_called_once_with(self.session_id)

    @patch("configurator.api.APIKeyAuth.authenticate")
    @patch("configurator.router.WorkbookSessionService")
    def test_session_not_found(self, mock_session_service_class, mock_auth):
        # Mock authentication to return the test user
        mock_auth.return_value = self.user

        # Mock WorkbookSessionService to return False (session not found)
        mock_session_service = Mock()
        mock_session_service.force_close_session.return_value = False
        mock_session_service_class.get_service.return_value = mock_session_service

        response = self.client.delete("/end-session", headers={"x-configurator-session-id": self.session_id})

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data["status"], "success")
        self.assertEqual(response_data["message"], "Session was not found or already ended")

        # Verify force_close_session was called with correct session_id
        mock_session_service.force_close_session.assert_called_once_with(self.session_id)

    @patch("configurator.api.APIKeyAuth.authenticate")
    @patch("configurator.router.WorkbookSessionService")
    def test_session_cleanup_error(self, mock_session_service_class, mock_auth):
        # Mock authentication to return the test user
        mock_auth.return_value = self.user

        # Mock WorkbookSessionService to raise an exception
        mock_session_service = Mock()
        mock_session_service.force_close_session.side_effect = Exception("Test error")
        mock_session_service_class.get_service.return_value = mock_session_service

        response = self.client.delete("/end-session", headers={"x-configurator-session-id": self.session_id})

        self.assertEqual(response.status_code, 422)
        response_data = response.json()
        self.assertEqual(len(response_data["detail"]), 1)
        self.assertEqual(response_data["detail"][0]["type"], "session_error")
        self.assertIn("Error ending session: Test error", response_data["detail"][0]["msg"])

        # Verify force_close_session was called with correct session_id
        mock_session_service.force_close_session.assert_called_once_with(self.session_id)

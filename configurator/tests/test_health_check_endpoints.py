# type: ignore

from unittest.mock import Mock, patch

from django.test import TestCase
from ninja.testing import TestClient

from configurator.router import router
from configurator.services.resource_monitor import ResourceMonitor


class HealthCheckEndpointsTest(TestCase):
    def setUp(self):
        self.client = TestClient(router)

    def test_basic_health_check_returns_ok(self):
        response = self.client.get("/health-check")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "ok"})

    @patch("configurator.router.WorkbookSessionService")
    def test_excel_health_check_success(self, mock_session_service_class):
        # Mock WorkbookSessionService health_check method
        mock_session_service = Mock()
        mock_session_service.health_check.return_value = {
            "status": "ok",
            "excel_test": "passed",
            "test_cell_accessible": True,
            "system_info": {
                "ram_usage_percent": 45.0,
                "cpu_usage_percent": 25.0,
                "excel_process_count": 1,
                "ram_threshold_exceeded": False,
                "cpu_threshold_exceeded": False,
                "max_ram_percent": 80,
                "max_cpu_percent": 90,
            },
            "session_count": 1,
            "excel_lock_metrics": {
                "total_acquisitions": 5,
                "total_wait_time_seconds": 0.123,
                "average_wait_time_seconds": 0.025,
                "max_wait_time_seconds": 0.050,
                "current_waiters": 0,
            },
        }
        mock_session_service_class.get_service.return_value = mock_session_service

        response = self.client.get("/health-check-excel")

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        self.assertEqual(response_data["status"], "ok")
        self.assertEqual(response_data["excel_test"], "passed")
        self.assertTrue(response_data["test_cell_accessible"])
        self.assertEqual(response_data["session_count"], 1)
        self.assertIn("system_info", response_data)
        self.assertIn("excel_lock_metrics", response_data)

        # Check lock metrics structure
        lock_metrics = response_data["excel_lock_metrics"]
        self.assertIn("total_acquisitions", lock_metrics)
        self.assertIn("average_wait_time_seconds", lock_metrics)
        self.assertIn("current_waiters", lock_metrics)

        # Verify health_check was called with default test_workbook=False
        mock_session_service.health_check.assert_called_once_with(test_workbook=False)

    @patch("configurator.router.WorkbookSessionService")
    def test_excel_health_check_with_test_workbook_disabled(self, mock_session_service_class):
        # Mock WorkbookSessionService health_check method for skipped workbook test
        mock_session_service = Mock()
        mock_session_service.health_check.return_value = {
            "status": "ok",
            "excel_test": "skipped",
            "test_cell_accessible": None,
            "system_info": {
                "ram_usage_percent": 45.0,
                "cpu_usage_percent": 25.0,
                "excel_process_count": 0,
                "ram_threshold_exceeded": False,
                "cpu_threshold_exceeded": False,
                "max_ram_percent": 80,
                "max_cpu_percent": 90,
            },
            "session_count": 0,
            "excel_lock_metrics": {
                "total_acquisitions": 1,
                "total_wait_time_seconds": 0.023,
                "average_wait_time_seconds": 0.023,
                "max_wait_time_seconds": 0.023,
                "current_waiters": 0,
            },
        }
        mock_session_service_class.get_service.return_value = mock_session_service

        response = self.client.get("/health-check-excel?test_workbook=false")

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        self.assertEqual(response_data["status"], "ok")
        self.assertEqual(response_data["excel_test"], "skipped")
        self.assertIsNone(response_data["test_cell_accessible"])
        self.assertEqual(response_data["session_count"], 0)
        self.assertIn("system_info", response_data)
        self.assertIn("excel_lock_metrics", response_data)

        # Check lock metrics structure is still present
        lock_metrics = response_data["excel_lock_metrics"]
        self.assertIn("total_acquisitions", lock_metrics)
        self.assertIn("average_wait_time_seconds", lock_metrics)
        self.assertIn("current_waiters", lock_metrics)

        # Verify health_check was called with test_workbook=False
        mock_session_service.health_check.assert_called_once_with(test_workbook=False)

    @patch("configurator.router.WorkbookSessionService")
    def test_excel_health_check_failure(self, mock_session_service_class):
        # Mock WorkbookSessionService health_check method to return failure
        mock_session_service = Mock()
        mock_session_service.health_check.return_value = {
            "status": "error",
            "excel_test": "failed",
            "error": "Excel not available",
            "system_info": {
                "ram_usage_percent": 45.0,
                "cpu_usage_percent": 25.0,
                "excel_process_count": 0,
                "ram_threshold_exceeded": False,
                "cpu_threshold_exceeded": False,
                "max_ram_percent": 80,
                "max_cpu_percent": 90,
            },
            "session_count": 0,
            "excel_lock_metrics": {
                "total_acquisitions": 2,
                "total_wait_time_seconds": 0.045,
                "average_wait_time_seconds": 0.023,
                "max_wait_time_seconds": 0.030,
                "current_waiters": 0,
            },
        }
        mock_session_service_class.get_service.return_value = mock_session_service

        response = self.client.get("/health-check-excel")

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        self.assertEqual(response_data["status"], "error")
        self.assertEqual(response_data["excel_test"], "failed")
        self.assertIn("error", response_data)
        self.assertEqual(response_data["session_count"], 0)
        self.assertIn("system_info", response_data)

    @patch("configurator.router.WorkbookSessionService")
    def test_excel_health_check_resource_monitor_failure(self, mock_session_service_class):
        # Mock WorkbookSessionService health_check method to return error
        mock_session_service = Mock()
        mock_session_service.health_check.return_value = {
            "status": "error",
            "error": "psutil not available",
        }
        mock_session_service_class.get_service.return_value = mock_session_service

        response = self.client.get("/health-check-excel")

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        self.assertEqual(response_data["status"], "error")
        self.assertIn("error", response_data)
        self.assertIn("psutil not available", response_data["error"])

    def test_resource_monitor_get_excel_processes_returns_list(self):
        with patch("configurator.services.resource_monitor.psutil") as mock_psutil:
            mock_process1 = Mock()
            mock_process1.info = {"pid": 1234, "name": "EXCEL.EXE"}
            mock_process2 = Mock()
            mock_process2.info = {"pid": 5678, "name": "notepad.exe"}
            mock_process3 = Mock()
            mock_process3.info = {"pid": 9999, "name": "Excel"}

            mock_psutil.process_iter.return_value = [mock_process1, mock_process2, mock_process3]

            resource_monitor = ResourceMonitor()
            excel_processes = resource_monitor.get_excel_processes()

            self.assertIsInstance(excel_processes, list)
            self.assertEqual(len(excel_processes), 2)  # Only Excel processes
            self.assertEqual(resource_monitor.get_excel_process_count(), 2)

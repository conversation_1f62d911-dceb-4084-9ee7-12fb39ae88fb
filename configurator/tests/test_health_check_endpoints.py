# type: ignore

from unittest.mock import Mock, patch

from django.test import TestCase
from ninja.testing import TestClient

from configurator.router import router
from configurator.services.resource_monitor import ResourceMonitor


class HealthCheckEndpointsTest(TestCase):
    def setUp(self):
        self.client = TestClient(router)

    def test_basic_health_check_returns_ok(self):
        response = self.client.get("/health-check")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "ok"})

    @patch("configurator.router.get_excel_service")
    def test_excel_health_check_success(self, mock_get_excel_service):
        # Mock Excel service for workbook test
        mock_excel_service = Mock()
        mock_excel_service.read_cell.return_value = "test_value"
        mock_get_excel_service.return_value = mock_excel_service

        response = self.client.get("/health-check-excel?test_workbook=true")

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        self.assertEqual(response_data["status"], "ok")
        self.assertEqual(response_data["excel_test"], "passed")
        self.assertTrue(response_data["test_cell_accessible"])

        # Verify Excel service was accessed
        mock_get_excel_service.assert_called_once()
        mock_excel_service.read_cell.assert_called_once_with("profileentry")

    def test_excel_health_check_with_test_workbook_disabled(self):
        # Test health check without workbook testing
        response = self.client.get("/health-check-excel?test_workbook=false")

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        self.assertEqual(response_data["status"], "ok")
        self.assertEqual(response_data["excel_test"], "skipped")
        self.assertIsNone(response_data["test_cell_accessible"])

    @patch("configurator.router.get_excel_service")
    def test_excel_health_check_failure(self, mock_get_excel_service):
        # Mock Excel service to raise an exception
        mock_get_excel_service.side_effect = Exception("Excel not available")

        response = self.client.get("/health-check-excel?test_workbook=true")

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        self.assertEqual(response_data["status"], "error")
        self.assertEqual(response_data["excel_test"], "failed")
        self.assertIn("error", response_data)

    def test_resource_monitor_get_excel_processes_returns_list(self):
        with patch("configurator.services.resource_monitor.psutil") as mock_psutil:
            mock_process1 = Mock()
            mock_process1.info = {"pid": 1234, "name": "EXCEL.EXE"}
            mock_process2 = Mock()
            mock_process2.info = {"pid": 5678, "name": "notepad.exe"}
            mock_process3 = Mock()
            mock_process3.info = {"pid": 9999, "name": "Excel"}

            mock_psutil.process_iter.return_value = [mock_process1, mock_process2, mock_process3]

            resource_monitor = ResourceMonitor()
            excel_processes = resource_monitor.get_excel_processes()

            self.assertIsInstance(excel_processes, list)
            self.assertEqual(len(excel_processes), 2)  # Only Excel processes
            self.assertEqual(resource_monitor.get_excel_process_count(), 2)

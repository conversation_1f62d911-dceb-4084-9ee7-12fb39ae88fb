import logging
import threading
from pathlib import Path

from django.conf import settings
from ninja import Router
from ninja.errors import ValidationError as NinjaValidationError

from configurator.services.excel import ExcelService, ExcelServiceError
from configurator.services.resource_monitor import ResourceMonitor

from .constants import PROFILES
from .schemas import EstimateRequest, EstimateResponse, ProfileInfo, UserSchema
from .services import WorkbookCorruptedError, WorkbookService, WorkbookServiceError

logger = logging.getLogger(__name__)

# Global lock to ensure only one estimate request processes at a time
_global_estimate_lock = threading.RLock()

router = Router()


@router.get("/health-check", auth=None)
def health_check(request):
    return {"status": "ok"}


@router.get("/system-stats")
def system_stats(request):
    """Get system resource statistics and information."""
    try:
        resource_monitor = ResourceMonitor()
        system_info = resource_monitor.get_system_info()
        return system_info
    except Exception as e:
        logger.exception("Failed to get system stats: %s", e)
        return {
            "status": "error",
            "error": str(e),
        }


@router.get("/auth/me", response=UserSchema)
def me(request):
    return request.auth


@router.get("/profile/{code}", response=ProfileInfo)
def get_profile_data(request, code: str):
    if code not in PROFILES:
        raise NinjaValidationError([{"msg": f"Invalid profile: {code}. Must be one of the valid profiles.", "type": "ValidationError"}])

    try:
        # Use global lock to ensure thread safety
        with _global_estimate_lock:
            excel_service = ExcelService.open(Path(settings.EXCEL_WORKBOOK_PATH), visible=settings.EXCEL_WORKBOOK_VISIBLE)
            try:
                workbook_service = WorkbookService(excel_service)
                profile_outputs = workbook_service.get_profile_data(code)

                profile_info = ProfileInfo(
                    profile=profile_outputs.profile,
                    profile_size=profile_outputs.profile_size,
                    extra=profile_outputs.extra.key if profile_outputs.extra else None,
                    material1=profile_outputs.material1.value if profile_outputs.material1 else "",
                    material2=profile_outputs.material2.value if profile_outputs.material2 else None,
                    material3=profile_outputs.material3.value if profile_outputs.material3 else None,
                )

                return profile_info
            finally:
                excel_service.close()

    except NinjaValidationError:
        raise
    except WorkbookCorruptedError as e:
        logger.error("Workbook corrupted while getting profile data: %s", e)
        raise NinjaValidationError([{"msg": "Workbook became corrupted. Please try again.", "type": "workbook_corrupted"}])
    except (WorkbookServiceError, ExcelServiceError) as e:
        logger.exception("Error getting profile data for %s: %s", code, e)
        raise NinjaValidationError([{"msg": str(e), "type": "workbook_error"}])
    except Exception as e:
        logger.exception("Error getting profile data for %s: %s", code, e)
        raise NinjaValidationError([{"msg": str(e), "type": "workbook_error"}])


@router.post("/estimate", response=EstimateResponse)
def estimate(request, data: EstimateRequest):
    try:
        # Use global lock to ensure only one estimate processes at a time
        with _global_estimate_lock:
            excel_service = ExcelService.open(Path(settings.EXCEL_WORKBOOK_PATH), visible=settings.EXCEL_WORKBOOK_VISIBLE)
            try:
                workbook_service = WorkbookService(excel_service)
                outputs, error_message = workbook_service.process(data)

                if error_message:
                    raise NinjaValidationError([{"msg": error_message, "type": "sheet_error"}])

                if outputs is None:
                    raise NinjaValidationError([{"msg": "No outputs generated from workbook", "type": "workbook_error"}])

                # Since EstimateResponse extends WorkbookOutputs, we can create it from the outputs data
                return EstimateResponse.model_validate(outputs.model_dump())
            finally:
                excel_service.close()

    except NinjaValidationError:
        raise
    except (WorkbookServiceError, ExcelServiceError) as e:
        logger.exception("Error processing estimate request: %s.", e)
        raise NinjaValidationError([{"msg": str(e), "type": "workbook_error"}])
    except Exception as e:
        logger.exception("Unexpected error processing estimate request: %s.", e)
        raise NinjaValidationError([{"msg": "An unexpected error occurred. Please try again.", "type": "server_error"}])

import logging
import threading

from django.conf import settings
from ninja import Router
from ninja.errors import ValidationError as NinjaValidationError

from configurator.services.excel import ExcelRPCError, ExcelService, ExcelServiceError

from .constants import PROFILES
from .schemas import EstimateRequest, EstimateResponse, ProfileInfo, UserSchema
from .services import WorkbookCorruptedError, WorkbookService, WorkbookServiceError

logger = logging.getLogger(__name__)

# Global lock to ensure only one estimate request processes at a time
_global_estimate_lock = threading.RLock()

router = Router()


@router.get("/health-check", auth=None)
def health_check(request):
    return {"status": "ok"}


@router.get("/health-check-excel")
def health_check_excel(request, test_workbook: bool = False):
    """Health check for Excel workbook operations."""
    try:
        if test_workbook:
            # Test basic workbook operations with global lock
            with _global_estimate_lock:
                excel_service = ExcelService.open(settings.EXCEL_WORKBOOK_PATH, visible=settings.EXCEL_WORKBOOK_VISIBLE)
                try:
                    # Test basic read operation
                    test_value = excel_service.read_cell("profileentry")
                    excel_accessible = test_value is not None
                finally:
                    excel_service.close()
        else:
            excel_accessible = True

        return {
            "status": "ok",
            "excel_test": "passed" if test_workbook else "skipped",
            "test_cell_accessible": excel_accessible if test_workbook else None,
        }
    except Exception as e:
        logger.exception("Excel health check failed: %s", e)
        return {
            "status": "error",
            "excel_test": "failed",
            "error": str(e),
        }


@router.get("/auth/me", response=UserSchema)
def me(request):
    return request.auth


@router.get("/profile/{code}", response=ProfileInfo)
def get_profile_data(request, code: str):
    if code not in PROFILES:
        raise NinjaValidationError([{"msg": f"Invalid profile: {code}. Must be one of the valid profiles.", "type": "ValidationError"}])

    try:
        # Use global lock to ensure thread safety
        with _global_estimate_lock:
            excel_service = ExcelService.open(settings.EXCEL_WORKBOOK_PATH, visible=settings.EXCEL_WORKBOOK_VISIBLE)
            try:
                workbook_service = WorkbookService(excel_service)
                profile_outputs = workbook_service.get_profile_data(code)

                profile_info = ProfileInfo(
                    profile=profile_outputs.profile,
                    profile_size=profile_outputs.profile_size,
                    extra=profile_outputs.extra.key if profile_outputs.extra else None,
                    material1=profile_outputs.material1.value if profile_outputs.material1 else "",
                    material2=profile_outputs.material2.value if profile_outputs.material2 else None,
                    material3=profile_outputs.material3.value if profile_outputs.material3 else None,
                )

                return profile_info
            finally:
                excel_service.close()

    except NinjaValidationError:
        raise
    except WorkbookCorruptedError as e:
        logger.error("Workbook corrupted while getting profile data: %s", e)
        raise NinjaValidationError([{"msg": "Workbook became corrupted. Please try again.", "type": "workbook_corrupted"}])
    except (WorkbookServiceError, ExcelServiceError) as e:
        logger.exception("Error getting profile data for %s: %s", code, e)
        raise NinjaValidationError([{"msg": str(e), "type": "workbook_error"}])
    except Exception as e:
        logger.exception("Error getting profile data for %s: %s", code, e)
        raise NinjaValidationError([{"msg": str(e), "type": "workbook_error"}])


@router.post("/estimate", response=EstimateResponse)
def estimate(request, data: EstimateRequest):
    try:
        # Use global lock to ensure only one estimate processes at a time
        with _global_estimate_lock:
            excel_service = ExcelService.open(settings.EXCEL_WORKBOOK_PATH, visible=settings.EXCEL_WORKBOOK_VISIBLE)
            try:
                workbook_service = WorkbookService(excel_service)
                outputs, error_message = workbook_service.process(data)

                if error_message:
                    raise NinjaValidationError([{"msg": error_message, "type": "sheet_error"}])

                if outputs is None:
                    raise NinjaValidationError([{"msg": "No outputs generated from workbook", "type": "workbook_error"}])

                # Since EstimateResponse extends WorkbookOutputs, we can create it from the outputs data
                return EstimateResponse.model_validate(outputs.model_dump())
            finally:
                excel_service.close()

    except NinjaValidationError:
        raise
    except WorkbookCorruptedError as e:
        logger.error("Workbook corrupted during estimate processing: %s", e)
        raise NinjaValidationError([{"msg": "Workbook became corrupted. Please try again.", "type": "workbook_corrupted"}])
    except ExcelRPCError as e:
        logger.error("Excel RPC error during estimate processing: %s", e)
        raise NinjaValidationError([{"msg": "Excel communication error. Please try again.", "type": "server_error"}])
    except (WorkbookServiceError, ExcelServiceError) as e:
        logger.exception("Error processing estimate request: %s.", e)
        raise NinjaValidationError([{"msg": str(e), "type": "workbook_error"}])

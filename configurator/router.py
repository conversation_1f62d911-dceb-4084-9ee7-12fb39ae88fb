import logging
import uuid

from ninja import Router
from ninja.errors import HttpError
from ninja.errors import ValidationError as NinjaValidationError

from configurator.services.excel import ExcelRPCError, ExcelServiceError

from .constants import PROFILES
from .schemas import EstimateRequest, EstimateResponse, ProfileInfo, UserSchema
from .services import WorkbookCorruptedError, WorkbookServiceError, WorkbookSessionService

logger = logging.getLogger(__name__)

router = Router()


@router.get("/health-check", auth=None)
def health_check(request):
    return {"status": "ok"}


@router.get("/health-check-excel")
def health_check_excel(request, test_workbook: bool = False):
    session_service = WorkbookSessionService.get_service()
    return session_service.health_check(test_workbook=test_workbook)


def get_session_id(request) -> str:
    session_id = request.headers.get("x-configurator-session-id")
    if not session_id:
        raise HttpError(400, "Missing x-configurator-session-id header")

    try:
        uuid.UUID(session_id, version=4)
    except ValueError:
        raise HttpError(400, "Invalid x-configurator-session-id format")

    return session_id


@router.get("/auth/me", response=UserSchema)
def me(request):
    return request.auth


@router.get("/profile/{code}", response=ProfileInfo)
def get_profile_data(request, code: str):
    session_id = get_session_id(request)

    if code not in PROFILES:
        raise NinjaValidationError([{"msg": f"Invalid profile: {code}. Must be one of the valid profiles.", "type": "ValidationError"}])

    try:
        session_service = WorkbookSessionService.get_service()

        with session_service.open_workbook_session(session_id) as workbook_service:
            profile_outputs = workbook_service.get_profile_data(code)

            profile_info = ProfileInfo(
                profile=profile_outputs.profile,
                profile_size=profile_outputs.profile_size,
                extra=profile_outputs.extra.key if profile_outputs.extra else None,
                material1=profile_outputs.material1.value if profile_outputs.material1 else "",
                material2=profile_outputs.material2.value if profile_outputs.material2 else None,
                material3=profile_outputs.material3.value if profile_outputs.material3 else None,
            )

            return profile_info

    except (HttpError, NinjaValidationError):
        raise
    except WorkbookCorruptedError as e:
        logger.error("Workbook corrupted for session %s while getting profile data: %s", session_id, e)
        # Use force_close_session to ensure Excel is properly closed and session is cleaned up
        session_service.force_close_session(session_id)
        raise NinjaValidationError(
            [{"msg": "Workbook became corrupted. Your session has been reset. Please try again.", "type": "workbook_corrupted"}]
        )
    except (WorkbookServiceError, ExcelServiceError) as e:
        logger.exception("Error getting profile data for %s: %s", code, e)
        session_service.cleanup_session(session_id)
        raise NinjaValidationError([{"msg": str(e), "type": "workbook_error"}])
    except Exception as e:
        logger.exception("Error getting profile data for %s: %s", code, e)
        session_service.cleanup_session(session_id)
        raise NinjaValidationError([{"msg": str(e), "type": "workbook_error"}])


@router.post("/estimate", response=EstimateResponse)
def estimate(request, data: EstimateRequest):
    session_id = get_session_id(request)

    try:
        session_service = WorkbookSessionService.get_service()

        # Use context manager to ensure proper cleanup of Excel resources
        with session_service.open_workbook_session(session_id) as workbook_service:
            outputs, error_message = workbook_service.process(data)

            if error_message:
                raise NinjaValidationError([{"msg": error_message, "type": "sheet_error"}])

            if outputs is None:
                raise NinjaValidationError([{"msg": "No outputs generated from workbook", "type": "workbook_error"}])

            # Since EstimateResponse extends WorkbookOutputs, we can create it from the outputs data
            return EstimateResponse.model_validate(outputs.model_dump())

    except (HttpError, NinjaValidationError):
        raise
    except WorkbookCorruptedError as e:
        logger.error("Workbook corrupted for session %s - forcing session cleanup: %s", session_id, e)
        # Use force_close_session to ensure Excel is properly closed and session is cleaned up
        session_service.force_close_session(session_id)
        raise NinjaValidationError(
            [{"msg": "Workbook became corrupted. Your session has been reset. Please try again.", "type": "workbook_corrupted"}]
        )
    except ExcelRPCError as e:
        logger.error("Excel RPC error - forcing cleanup of all sessions: %s", e)
        # For RPC errors, the Excel COM server may be corrupted, so clean up everything
        session_service.force_cleanup_all_sessions()
        raise NinjaValidationError([{"msg": "Excel communication error. Please try again.", "type": "server_error"}])
    except (WorkbookServiceError, ExcelServiceError) as e:
        logger.exception("Error processing estimate request: %s.", e)
        session_service.cleanup_session(session_id)
        raise NinjaValidationError([{"msg": str(e), "type": "workbook_error"}])


@router.delete("/end-session")
def end_session(request):
    """
    End a configurator session and clean up associated resources.

    This endpoint allows clients to explicitly terminate their session,
    which force closes the Excel workbook and deletes the temporary session file.
    Sessions are also automatically cleaned up after 5 minutes of inactivity.
    """
    session_id = get_session_id(request)

    try:
        session_service = WorkbookSessionService.get_service()
        # Use force_close_session since this is an explicit user action to end the session
        session_existed = session_service.force_close_session(session_id)

        if session_existed:
            logger.info("Session %s ended and Excel workbook force closed", session_id)
            return {"status": "success", "message": "Session ended and Excel workbook closed"}
        else:
            logger.info("Session %s was not found or already ended", session_id)
            return {"status": "success", "message": "Session was not found or already ended"}

    except Exception as e:
        logger.exception("Error ending session %s: %s", session_id, e)
        raise NinjaValidationError([{"msg": f"Error ending session: {str(e)}", "type": "session_error"}])

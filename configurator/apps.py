import atexit
import logging
import signal

from django.apps import AppConfig

logger = logging.getLogger(__name__)


class ConfiguratorConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "configurator"

    def ready(self):
        """Called when Django starts up."""
        self.setup_signal_handlers()

    def setup_signal_handlers(self):

        def cleanup_handler(signum, frame):
            from configurator.services.resource_monitor import ResourceMonitor

            logger.debug("Received signal %s, cleaning up Excel processes...", signum)

            try:
                resource_monitor = ResourceMonitor()
                excel_processes = resource_monitor.get_excel_processes()

                killed_count = 0
                for process in excel_processes:
                    try:
                        process.terminate()
                        killed_count += 1
                        logger.debug("Terminated Excel process: %s", process.pid)
                    except Exception as e:
                        logger.warning("Error terminating Excel process: %s", e)

                logger.debug("Cleanup completed successfully - killed %d Excel processes", killed_count)

            except Exception as e:
                logger.error("Error during cleanup: %s", e)

        # Register Windows compatible signal handlers
        signal.signal(signal.SIGTERM, cleanup_handler)  # Standard termination
        signal.signal(signal.SIGINT, cleanup_handler)  # Ctrl+C

        # Also register atexit handler as backup
        atexit.register(self.atexit_cleanup)

    def atexit_cleanup(self):
        """Cleanup function called at exit."""
        try:
            from configurator.services.resource_monitor import ResourceMonitor

            resource_monitor = ResourceMonitor()
            excel_processes = resource_monitor.get_excel_processes()

            for process in excel_processes:
                try:
                    process.terminate()
                except Exception as e:
                    logger.warning("Error terminating Excel process during exit: %s", e)

        except Exception as e:
            logger.error("Error during atexit cleanup: %s", e)

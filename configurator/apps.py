import atexit
import logging
import signal

from django.apps import AppConfig

logger = logging.getLogger(__name__)


class ConfiguratorConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "configurator"

    def ready(self):
        """Called when Django starts up."""
        self.setup_signal_handlers()

    def setup_signal_handlers(self):

        def cleanup_handler(signum, frame):
            from configurator.router import reset_excel_service

            logger.debug("Received signal %s, cleaning up persistent Excel connection...", signum)

            try:
                # Close persistent Excel connection
                reset_excel_service()
                logger.debug("Persistent Excel connection cleanup completed successfully")

            except Exception as e:
                logger.error("Error during cleanup: %s", e)

        # Register Windows compatible signal handlers
        signal.signal(signal.SIGTERM, cleanup_handler)  # Standard termination
        signal.signal(signal.SIGINT, cleanup_handler)  # Ctrl+C

        # Also register atexit handler as backup
        atexit.register(self.atexit_cleanup)

    def atexit_cleanup(self):
        """Cleanup function called at exit."""
        try:
            from configurator.router import reset_excel_service

            # Close persistent Excel connection
            reset_excel_service()
            logger.debug("Persistent Excel connection closed during exit")

        except Exception as e:
            logger.error("Error during atexit cleanup: %s", e)

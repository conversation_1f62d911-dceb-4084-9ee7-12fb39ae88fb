from typing import List

MATERIALS: List[str] = [
    "A200",
    "AFLS",
    "ALUM",
    "AUSC100",
    "BRAS",
    "EPDF",
    "EPDM",
    "EPDW",
    "ERTA",
    "FP85",
    "HNBR",
    "NB85",
    "PA",
    "PEEK",
    "POMD",
    "PU57",
    "PU95",
    "SF85",
    "SI85",
    "TEF15G",
    "TEF232",
    "TEF250",
    "TEF25G",
    "TEF600",
    "TEFD05",
    "TEFT",
    "TEFV",
    "TGRA",
    "TMB",
    "PU57SL",
]

PROFILES: List[str] = [
    "EP127",
    "EP138",
    "EP222",
    "HA101",
    "HA102",
    "HA103",
    "HA104",
    "HA105",
    "HA106",
    "HA107",
    "HA108",
    "HA109",
    "HA111",
    "HA112",
    "HA113",
    "HA114",
    "HA115",
    "HA116",
    "HA211",
    "HA213",
    "HF101",
    "HF102",
    "HF103",
    "HF104",
    "HF105",
    "HF106",
    "HF107",
    "HF108",
    "HFL101",
    "HFL102",
    "HFL103",
    "HFL104",
    "HFL106",
    "HFL108",
    "HK101",
    "HK102",
    "HK103",
    "HK104",
    "HK105",
    "HK106",
    "HK107",
    "HK108",
    "HK109",
    "HK110",
    "HK116",
    "HK117",
    "HK118",
    "HK119",
    "HK120",
    "HK122",
    "HK123",
    "HK124",
    "HK125",
    "HK126",
    "HK127",
    "HK138",
    "HK140",
    "HK222",
    "HLF108",
    "HR101",
    "HR102",
    "HR103",
    "HR104",
    "HR105",
    "HR106",
    "HR108",
    "HR110",
    "HR111",
    "HR112",
    "HR115",
    "HR116",
    "HR117",
    "HR201",
    "HR202",
    "HS101",
    "HS102",
    "HS103",
    "HS104",
    "HS105",
    "HS106",
    "HS107",
    "HS108",
    "HS109",
    "HS110",
    "HS116",
    "HS117",
    "HS118",
    "HS119",
    "HS120",
    "HS121",
    "HS124",
    "HS125",
    "HS129",
    "HS130",
    "HS131",
    "HS138",
    "HS139",
    "HS238",
    "HST108",
    "HST109",
    "HST110",
    "HST111",
    "HST112",
    "HST113",
    "HST114",
]

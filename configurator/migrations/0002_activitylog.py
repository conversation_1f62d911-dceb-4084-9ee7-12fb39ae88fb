# Generated by Django 5.2.3 on 2025-06-30 03:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("configurator", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ActivityLog",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("text", models.TextField(blank=True, null=True)),
                (
                    "type",
                    models.CharField(
                        choices=[("INFO", "Info"), ("WARNING", "Warning"), ("ERROR", "Error"), ("DEBUG", "Debug")], default="INFO", max_length=10
                    ),
                ),
                ("data", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ("-created_at",),
                "abstract": False,
            },
        ),
    ]

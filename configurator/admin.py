from django.contrib import admin

from .models import ActivityLog, APIKey


@admin.register(APIKey)
class APIKeyAdmin(admin.ModelAdmin):
    list_display = ("key", "user", "active")
    search_fields = ("key", "user__username")
    list_filter = ("active",)


@admin.register(ActivityLog)
class ActivityLogAdmin(admin.ModelAdmin):
    list_display = ("text", "created_at", "data")
    list_filter = ("created_at",)
    search_fields = ("text", "data")

"""
Django management command to monitor system resources and cleanup when thresholds are exceeded.

This command checks current CPU and RAM usage. When either threshold is exceeded,
it automatically cleans up all Excel processes to free up system resources.
Designed to be run periodically (e.g., every 5 minutes) via cron or scheduler.
"""

import logging

import djclick as click

from configurator.services.resource_monitor import ResourceMonitor, ResourceMonitorError

logger = logging.getLogger(__name__)


@click.command()
def command():
    """Monitor system resources and cleanup when thresholds are exceeded."""

    try:
        resource_monitor = ResourceMonitor()
        _check_and_cleanup(resource_monitor)

    except ResourceMonitorError as e:
        click.echo(click.style(f"Resource monitor error: {e}", fg="red"))
        raise click.ClickException(str(e))
    except Exception as e:
        click.echo(click.style(f"Unexpected error: {e}", fg="red"))
        raise


def _check_and_cleanup(resource_monitor: ResourceMonitor):
    """Check resource usage and perform cleanup if thresholds are exceeded."""

    try:
        # Get current system info
        system_info = resource_monitor.get_system_info()

        ram_usage = system_info.get("ram_usage_percent", 0)
        cpu_usage = system_info.get("cpu_usage_percent", 0)
        ram_exceeded = system_info.get("ram_threshold_exceeded", False)
        cpu_exceeded = system_info.get("cpu_threshold_exceeded", False)

        # Check if cleanup is needed
        if ram_exceeded or cpu_exceeded:
            threshold_type = []
            if ram_exceeded:
                threshold_type.append(f"RAM ({ram_usage:.1f}%)")
            if cpu_exceeded:
                threshold_type.append(f"CPU ({cpu_usage:.1f}%)")

            threshold_msg = " and ".join(threshold_type)
            click.echo(f"⚠️  {threshold_msg} threshold exceeded - performing cleanup")

            # Perform cleanup
            cleanup_results = _perform_emergency_cleanup(resource_monitor)

            # Report cleanup results
            excel_killed = cleanup_results["excel_killed"]

            click.echo(f"✅ Cleanup completed: {excel_killed} Excel processes killed")

            logger.info(f"Emergency cleanup triggered - {threshold_msg} exceeded. " f"Killed {excel_killed} Excel processes")

    except Exception as e:
        logger.error("Error during resource check: %s", e)
        click.echo(f"Error checking resources: {e}")


def _perform_emergency_cleanup(resource_monitor: ResourceMonitor) -> dict:
    """Perform emergency cleanup of Excel processes."""

    try:
        # Cleanup Excel processes
        excel_processes = resource_monitor.get_excel_processes()
        excel_killed = 0

        for process in excel_processes:
            try:
                process.terminate()
                excel_killed += 1
                logger.debug("Terminated Excel process: %s", process.pid)
            except Exception as e:
                logger.warning("Error terminating Excel process: %s", e)

        return {
            "excel_killed": excel_killed,
        }

    except Exception as e:
        logger.error("Error during emergency cleanup: %s", e)
        return {
            "excel_killed": 0,
            "error": str(e),
        }

"""
Django management command to monitor VBA dialogs and automatically dismiss them.

This command starts a long-running process that continuously monitors for
VBA error dialogs and automatically dismisses them to prevent Excel operations
from hanging.

Usage:
    python manage.py monitor_vba_dialogs
    python manage.py monitor_vba_dialogs --interval 0.5 --timeout 3.0
    python manage.py monitor_vba_dialogs --log-level DEBUG
"""

import logging
import signal
import time
from typing import Optional

import djclick as click

from configurator.services.vba_dialog_detector import VBADialogDetector

logger = logging.getLogger(__name__)


@click.command()
@click.option("--interval", type=float, default=1.0, help="Detection interval in seconds (default: 1.0)")
@click.option("--timeout", type=float, default=2.0, help="Detection timeout in seconds (default: 2.0)")
def command(interval: float, timeout: float):
    """Monitor VBA dialogs and automatically dismiss them."""

    click.echo(click.style(f"Starting VBA dialog monitoring (interval: {interval}s, timeout: {timeout}s)", fg="green"))

    # Create the detector
    detector = VBADialogDetector(timeout=timeout)

    shutdown = {"flag": False}

    def signal_handler(signum, frame):
        signal_name = signal.Signals(signum).name
        logger.info("Received %s signal, shutting down...", signal_name)
        shutdown["flag"] = True

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        while not shutdown["flag"]:
            try:
                dialogs = detector.check_for_dialogs()

                if dialogs:
                    for dialog_info in dialogs:
                        _on_dialog_detected(dialog_info.title, dialog_info.error_message)

            except Exception as e:
                _on_error(e)

            time.sleep(interval)

    except KeyboardInterrupt:
        logger.info("Received interrupt signal...")
    except Exception as e:
        logger.error("Error running VBA dialog monitoring: %s", e)
        raise click.ClickException(str(e))
    finally:
        click.echo(click.style("VBA dialog monitoring stopped.", fg="green"))


def _on_dialog_detected(title: str, error_message: Optional[str]):
    """Handle when a VBA dialog is detected."""
    if error_message:
        logger.warning("VBA Dialog Detected: '%s' - Error: %s", title, error_message)
    else:
        logger.info("VBA Dialog Detected: '%s'", title)


def _on_error(error: Exception):
    """Handle when an error occurs during detection."""
    logger.error("Detection Error: %s", error)

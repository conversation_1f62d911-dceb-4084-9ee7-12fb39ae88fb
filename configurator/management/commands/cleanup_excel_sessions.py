"""
Django management command to cleanup all workbook sessions and Excel processes.

This command uses the WorkbookSessionService to perform comprehensive cleanup:
- Removes all active sessions and temporary workbook files
- Cleans up Excel lock files
- Terminates all Excel processes
- Provides detailed reporting of cleanup actions
"""

import djclick as click

from configurator.services import WorkbookSessionService


@click.command()
def command():
    """Cleanup all workbook sessions, temporary files, and Excel processes."""

    try:
        session_service = WorkbookSessionService.get_service()

        # Clean up sessions and orphaned files
        sessions_cleaned = session_service.cleanup_all_sessions()
        if sessions_cleaned > 0:
            click.echo(f"✅ Cleaned up {sessions_cleaned} sessions and orphaned files")

        # Clean up Excel lock files
        lock_files_cleaned = session_service.cleanup_excel_lock_files()
        if lock_files_cleaned > 0:
            click.echo(f"✅ Cleaned up {lock_files_cleaned} Excel lock files")

        # Clean up Excel processes
        excel_processes_killed = session_service.cleanup_excel_processes()
        if excel_processes_killed > 0:
            click.echo(f"✅ Killed {excel_processes_killed} Excel process(es)")

        # Summary message
        total_cleaned = sessions_cleaned + lock_files_cleaned + excel_processes_killed
        if total_cleaned > 0:
            click.echo(click.style("Successfully completed comprehensive cleanup", fg="green"))
        else:
            click.echo(click.style("No sessions, files, or processes found to clean up", fg="green"))

    except Exception as e:
        click.echo(click.style(f"Error during cleanup: {str(e)}", fg="red"))
        raise

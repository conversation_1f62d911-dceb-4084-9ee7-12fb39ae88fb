"""
Django management command to kill all Excel processes.

This command uses the WorkbookSessionService to forcefully terminate all
Excel processes running on the system. Useful for emergency cleanup or
when Excel processes become unresponsive.
"""

import djclick as click

from configurator.services import WorkbookSessionService


@click.command()
def command():
    try:
        killed_count = WorkbookSessionService.get_service().cleanup_excel_processes()

        if killed_count > 0:
            click.echo(click.style(f"Successfully killed {killed_count} Excel process(es)", fg="green"))
        else:
            click.echo(click.style("No Excel processes found to kill", fg="green"))

    except RuntimeError as e:
        click.echo(click.style(f"Error: {str(e)}", fg="red"))
    except Exception as e:
        click.echo(click.style(f"Unexpected error: {str(e)}", fg="red"))
        raise

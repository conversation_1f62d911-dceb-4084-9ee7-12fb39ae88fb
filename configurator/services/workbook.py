"""
Configurator workbook service for business logic operations.

This service provides high-level business operations for the Excel configurator workbook,
including input validation, output reading, and error detection.
"""

import logging
import time
from typing import Any, Dict, Optional, Tuple

from ..schemas import (
    ExtraInfo,
    MaterialDetails,
    MaterialInfo,
    Materials,
    Measure,
    Part,
    Price,
    ProfileOutputs,
    WorkbookInputs,
    WorkbookOutputs,
)
from .excel import ExcelService

logger = logging.getLogger(__name__)


class WorkbookServiceError(Exception):
    """Base exception for WorkbookService errors."""


class WorkbookCorruptedError(WorkbookServiceError):
    """Raised when the workbook becomes corrupted and the session should be cleaned up."""


class WorkbookCells:
    """Static class containing all Excel cell references for the configurator workbook."""

    # Input/Output cells (used for both reading and writing)
    profile = "profileentry"
    measure = "measureentry"
    od = "ODentry"
    id = "IDentry"
    ch = "CHentry"
    extra = "extraentry"
    material1 = "MATL1entry"
    material2 = "MATL2entry"
    material3 = "MATL3entry"

    # Output-only cells
    profile_size = "D6"
    extratype = "extratype"
    type1 = "Type1"
    type2 = "type2"
    type3 = "Type3"
    part_code = "C31"
    part_description = "partdesc"
    cost_price = "costpriceevco"
    abst_price = "C19"
    b1_price = "C20"
    retail_price = "C21"
    billet_required1 = "billetreqd1"
    mc_time1 = "mctime1"
    mm_required1 = "mmreqd1"
    billet_required2 = "billetreqd2"
    mc_time2 = "mctime2"
    mm_required2 = "mmreqd2"
    billet_required3 = "billetreqd3"
    mc_time3 = "mctime3"
    mm_required3 = "mmreqd3"


class WorkbookService:
    """
    Service for high-level configurator workbook operations.

    Provides business logic operations including input validation, output reading,
    and error detection for the Excel configurator workbook.

    Can be used as a context manager to ensure proper cleanup of Excel resources.
    """

    def __init__(self, excel_service: ExcelService):
        self.excel_service = excel_service

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - ensures Excel resources are cleaned up."""
        if self.excel_service:
            self.excel_service.close()

    def _validate(self, inputs: WorkbookInputs) -> bool:
        """
        Validate inputs against the current workbook state.

        Returns:
            True if all required fields are provided and no invalid fields are present.
            False if validation fails (missing required fields or invalid fields provided).
        """
        # Read all validation cells in a single batch operation
        validation_cells = self.excel_service.read_cells_batch(
            [
                WorkbookCells.extratype,
                WorkbookCells.type2,
                WorkbookCells.type3,
            ]
        )

        extratype = validation_cells.get(WorkbookCells.extratype)
        type2 = validation_cells.get(WorkbookCells.type2)
        type3 = validation_cells.get(WorkbookCells.type3)

        # Check extra field validation
        extratype_has_value = extratype and str(extratype).strip()

        if extratype_has_value:
            if inputs.extra is None:
                return False  # Missing required extra value
        else:
            if inputs.extra is not None:
                return False  # Extra value provided when not needed

        # Check material2 field validation
        type2_has_value = type2 and str(type2).strip()

        if type2_has_value:
            if inputs.material2 is None:
                return False  # Missing required material2
        else:
            if inputs.material2 is not None:
                return False  # Material2 provided when not needed

        # Check material3 field validation
        type3_has_value = type3 and str(type3).strip()

        if type3_has_value:
            if inputs.material3 is None:
                return False  # Missing required material3
        else:
            if inputs.material3 is not None:
                return False  # Material3 provided when not needed

        return True  # All validation checks passed

    def _write_inputs(self, inputs: WorkbookInputs) -> bool:
        """
        Write input values to the workbook in the correct sequence.

        Args:
            inputs: Input data to write to the workbook

        Returns:
            True if all required fields are provided, False otherwise
        """
        # Write profile first as it determines what extra/material fields are needed
        self.excel_service.write_cell(WorkbookCells.profile, inputs.profile)

        # Now validate inputs against the updated workbook state
        inputs_valid = self._validate(inputs)

        # Write remaining core inputs
        remaining_core_inputs = [
            ("measure", inputs.measure, WorkbookCells.measure),
            ("od", inputs.od, WorkbookCells.od),
            ("id", inputs.id, WorkbookCells.id),
            ("ch", inputs.ch, WorkbookCells.ch),
        ]

        input_sequence = remaining_core_inputs.copy()

        if inputs.extra is not None:
            input_sequence.append(("extra", inputs.extra, WorkbookCells.extra))

        input_sequence.extend([("material1", inputs.material1, WorkbookCells.material1)])

        if inputs.material2 is not None:
            input_sequence.append(("material2", inputs.material2, WorkbookCells.material2))
        if inputs.material3 is not None:
            input_sequence.append(("material3", inputs.material3, WorkbookCells.material3))

        for i, (_, value, cell_ref) in enumerate(input_sequence):
            self.excel_service.write_cell(cell_ref, value)

        return inputs_valid

    def _calculate_pricing(self) -> bool:
        """
        Execute the pricing calculation macro with retry logic.

        Returns:
            True if macro execution succeeded, False if it failed after all retries
        """
        max_retries = 3

        for attempt in range(max_retries):
            try:
                self.excel_service.execute_macro("runme")

                if not self._needs_price_calculation():
                    return True  # Success

                logger.warning("Macro execution attempt %d resulted in 'refresh data' outputs", attempt + 1)

            except Exception as e:
                logger.warning("Macro execution attempt %d failed: %s", attempt + 1, e)

            # Add delay before retry
            if attempt < max_retries - 1:
                time.sleep(0.5)

        logger.error("Macro execution failed after %d attempts - workbook is corrupted", max_retries)
        return False

    def _needs_price_calculation(self) -> bool:
        """Check if output cells contain 'refresh data' indicating incomplete macro execution."""
        try:
            # Check a few key output cells for "refresh data"
            test_cells = [WorkbookCells.cost_price, WorkbookCells.mc_time1, WorkbookCells.part_code]
            cell_values = self.excel_service.read_cells_batch(test_cells)

            for cell_value in cell_values.values():
                if cell_value and "refresh data" in str(cell_value).lower():
                    return True
            return False
        except Exception:
            # If we can't check, assume it's safe to proceed
            return False

    def _read_outputs(self) -> WorkbookOutputs:
        """
        Read all output values from the workbook using batch operation for improved performance.

        Returns:
            Structured output data from the workbook
        """
        # Define all cells to read in a single batch operation
        all_output_cells = [
            # Profile outputs
            WorkbookCells.profile,
            WorkbookCells.profile_size,
            WorkbookCells.measure,
            WorkbookCells.od,
            WorkbookCells.id,
            WorkbookCells.ch,
            WorkbookCells.extratype,
            WorkbookCells.extra,
            WorkbookCells.type1,
            WorkbookCells.material1,
            WorkbookCells.type2,
            WorkbookCells.material2,
            WorkbookCells.type3,
            WorkbookCells.material3,
            # Part information
            WorkbookCells.part_code,
            WorkbookCells.part_description,
            # Pricing information
            WorkbookCells.cost_price,
            WorkbookCells.abst_price,
            WorkbookCells.b1_price,
            WorkbookCells.retail_price,
            # Materials information
            WorkbookCells.billet_required1,
            WorkbookCells.mc_time1,
            WorkbookCells.mm_required1,
            WorkbookCells.billet_required2,
            WorkbookCells.mc_time2,
            WorkbookCells.mm_required2,
            WorkbookCells.billet_required3,
            WorkbookCells.mc_time3,
            WorkbookCells.mm_required3,
        ]

        # Read all cells in a single batch operation
        cell_values = self.excel_service.read_cells_batch(all_output_cells)

        # Build structured output using cached values
        profile_outputs = self._build_profile_outputs(cell_values)
        part = self._build_part_info(cell_values)
        price = self._build_price_info(cell_values)
        materials = self._build_materials_info(cell_values)

        return WorkbookOutputs(profile_outputs=profile_outputs, part=part, price=price, materials=materials)

    def _build_profile_outputs(self, cell_values: Dict[str, Any]) -> ProfileOutputs:
        """Build profile outputs from cached cell values."""
        # Read core measure data
        measure = Measure(
            measure=str(cell_values.get(WorkbookCells.measure, "")),
            od=self._format_cached_float(cell_values.get(WorkbookCells.od)),
            id=self._format_cached_float(cell_values.get(WorkbookCells.id)),
            ch=self._format_cached_float(cell_values.get(WorkbookCells.ch)),
        )

        return ProfileOutputs(
            profile=str(cell_values.get(WorkbookCells.profile, "")),
            profile_size=str(cell_values.get(WorkbookCells.profile_size, "")),
            measure=measure,
            extra=self._build_extra_info(cell_values),
            material1=self._build_material_info(cell_values, WorkbookCells.type1, WorkbookCells.material1),
            material2=self._build_optional_material_info(cell_values, WorkbookCells.type2, WorkbookCells.material2),
            material3=self._build_optional_material_info(cell_values, WorkbookCells.type3, WorkbookCells.material3),
        )

    def _build_extra_info(self, cell_values: Dict[str, Any]) -> Optional[ExtraInfo]:
        """Build extra information from cached cell values."""
        extratype = cell_values.get(WorkbookCells.extratype)
        extra_value = cell_values.get(WorkbookCells.extra)

        if extratype and str(extratype).strip() and extra_value is not None:
            try:
                return ExtraInfo(key=str(extratype).strip(), value=int(extra_value))
            except (ValueError, TypeError):
                logger.warning("Could not convert extra_value to int: %s, skipping extra info", extra_value)
                return None
        return None

    def _format_cached_float(self, value: Any) -> float:
        """
        Format a cached float value to 3 decimal places to match Excel display format.

        This prevents floating-point precision errors where Excel shows "21.654"
        but Python reads it as "21.653543307086615".
        Also handles "refresh data" and other non-numeric values.
        """
        if value is None:
            return 0.0

        try:
            return round(float(str(value)), 3)
        except (ValueError, TypeError):
            # Handle cases where value is "refresh data" or other non-numeric strings
            logger.warning("Could not convert value to float: %s, using 0.0", value)
            return 0.0

    def _build_material_info(self, cell_values: Dict[str, Any], type_cell: str, material_cell: str) -> MaterialInfo:
        """Build material information from cached cell values."""
        type_name = cell_values.get(type_cell)
        material_value = cell_values.get(material_cell)

        return MaterialInfo(
            name=str(type_name) if type_name else "",
            value=str(material_value) if material_value else "",
        )

    def _build_optional_material_info(self, cell_values: Dict[str, Any], type_cell: str, material_cell: str) -> Optional[MaterialInfo]:
        """Build optional material information from cached cell values."""
        type_name = cell_values.get(type_cell)
        material_value = cell_values.get(material_cell)

        # Only return MaterialInfo if both type and material have values
        if type_name and str(type_name).strip() and material_value and str(material_value).strip():
            return MaterialInfo(
                name=str(type_name).strip(),
                value=str(material_value).strip(),
            )
        return None

    def _build_part_info(self, cell_values: Dict[str, Any]) -> Part:
        """Build part information from cached cell values."""
        return Part(
            code=str(cell_values.get(WorkbookCells.part_code, "")),
            description=str(cell_values.get(WorkbookCells.part_description, "")),
        )

    def _build_price_info(self, cell_values: Dict[str, Any]) -> Price:
        """Build pricing information from cached cell values."""
        return Price(
            cost=str(cell_values.get(WorkbookCells.cost_price, "")),
            abst=str(cell_values.get(WorkbookCells.abst_price, "")),
            b1=str(cell_values.get(WorkbookCells.b1_price, "")),
            retail=str(cell_values.get(WorkbookCells.retail_price, "")),
        )

    def _build_materials_info(self, cell_values: Dict[str, Any]) -> Materials:
        """Build materials information from cached cell values."""
        # Material 1 is always required
        billet1 = cell_values.get(WorkbookCells.billet_required1)
        time1 = cell_values.get(WorkbookCells.mc_time1)
        mm1 = cell_values.get(WorkbookCells.mm_required1)

        material1 = MaterialDetails(
            billet_required=str(billet1) if billet1 else "",
            min_manufacturing_time=self._safe_float_conversion(time1),
            billet_material_required=str(mm1) if mm1 else "",
        )

        # Material 2 is optional
        material2 = None
        billet2 = cell_values.get(WorkbookCells.billet_required2)
        time2 = cell_values.get(WorkbookCells.mc_time2)
        mm2 = cell_values.get(WorkbookCells.mm_required2)

        if billet2 and str(billet2).strip():
            material2 = MaterialDetails(
                billet_required=str(billet2),
                min_manufacturing_time=self._safe_float_conversion(time2),
                billet_material_required=str(mm2) if mm2 else "",
            )

        # Material 3 is optional
        material3 = None
        billet3 = cell_values.get(WorkbookCells.billet_required3)
        time3 = cell_values.get(WorkbookCells.mc_time3)
        mm3 = cell_values.get(WorkbookCells.mm_required3)

        if billet3 and str(billet3).strip():
            material3 = MaterialDetails(
                billet_required=str(billet3),
                min_manufacturing_time=self._safe_float_conversion(time3),
                billet_material_required=str(mm3) if mm3 else "",
            )

        return Materials(material1=material1, material2=material2, material3=material3)

    def _safe_float_conversion(self, value: Any) -> float:
        """
        Safely convert a value to float, handling 'refresh data' and other non-numeric values.

        Args:
            value: Value to convert to float

        Returns:
            Float value or 0.0 if conversion fails
        """
        if value is None:
            return 0.0

        try:
            return float(str(value))
        except (ValueError, TypeError):
            # Handle cases where value is "refresh data" or other non-numeric strings
            logger.warning("Could not convert value to float: %s, using 0.0", value)
            return 0.0

    def _detect_error(self) -> Optional[str]:
        """
        Detect errors in the workbook.

        Returns:
            Error message if an error is detected, None otherwise
        """
        try:
            # Check for material/size unavailable first
            material_error = self._detect_material_unavailable()
            if material_error:
                return material_error

            cost_price = self.excel_service.read_cell(WorkbookCells.cost_price)
            if cost_price is None:
                return None

            if str(cost_price) == "0.00":
                logger.warning("Cost price is 0, indicating a sheet error - checking partdesc for error message")
                return self._get_sheet_error()
        except (ValueError, TypeError) as e:
            logger.exception("Exception raised when checking for workbook errors: %s", e)

        return None

    def _detect_material_unavailable(self) -> Optional[str]:
        """
        Detect if material/size is not available and return error message.

        Returns:
            Error message if material/size not available, None otherwise
        """
        try:
            part_description = self.excel_service.read_cell(WorkbookCells.part_description)
            cost_price = self.excel_service.read_cell(WorkbookCells.cost_price)

            if part_description and "Material/Size not available" in str(part_description):
                error_message = str(part_description)

                # Check if cost_price contains additional string information
                if cost_price and isinstance(cost_price, str) and cost_price.strip():
                    # Append cost_price text if it's not a numeric value
                    try:
                        float(cost_price)
                    except (ValueError, TypeError):
                        error_message += f". {cost_price.strip()}."

                return error_message
        except (ValueError, TypeError) as e:
            logger.exception("Exception raised when checking for material unavailable: %s", e)

        return None

    def _needs_refresh(self) -> bool:
        """Check if the workbook needs a data refresh."""
        return "refresh data" in str(self.excel_service.read_cell(WorkbookCells.part_description)).lower()

    def _get_sheet_error(self) -> Optional[str]:
        """Get error message from the part description cell."""
        return self.excel_service.read_cell(WorkbookCells.part_description)

    def get_error_message(self) -> Optional[str]:
        """Get any error message from the workbook."""
        return self._detect_error()

    def get_profile_data(self, profile_code: str) -> ProfileOutputs:
        self.excel_service.write_cell(WorkbookCells.profile, profile_code)

        # Read profile-related cells in batch
        profile_cells = [
            WorkbookCells.profile,
            WorkbookCells.profile_size,
            WorkbookCells.measure,
            WorkbookCells.od,
            WorkbookCells.id,
            WorkbookCells.ch,
            WorkbookCells.extratype,
            WorkbookCells.extra,
            WorkbookCells.type1,
            WorkbookCells.material1,
            WorkbookCells.type2,
            WorkbookCells.material2,
            WorkbookCells.type3,
            WorkbookCells.material3,
        ]

        cell_values = self.excel_service.read_cells_batch(profile_cells)
        return self._build_profile_outputs(cell_values)

    def process(self, inputs: WorkbookInputs) -> Tuple[Optional[WorkbookOutputs], Optional[str]]:
        """
        Process inputs through the complete workbook pipeline.

        Args:
            inputs: Input data to process

        Returns:
            Structured output data from the workbook
        """
        # Use operation-level locking (Excel service handles locking internally)
        return self._process_internal(inputs)

    def _process_internal(self, inputs: WorkbookInputs) -> Tuple[Optional[WorkbookOutputs], Optional[str]]:
        """
        Internal processing method with timing and business logic.

        This method contains the actual processing logic and is called by process()
        with or without session-level locking depending on configuration.
        """
        start_time = time.time()

        # Get workbook name prefix for instrumentation
        workbook_name = self.excel_service.workbook_path.name[:4] if self.excel_service.workbook_path else "unknown"

        # Write inputs timing
        write_start = time.time()
        inputs_valid = self._write_inputs(inputs)
        write_time = time.time() - write_start
        logger.info("[%s] Input writing took %.3f seconds", workbook_name, write_time)

        # Pricing calculation timing
        calc_start = time.time()
        # Only run pricing calculation if all required inputs are valid or workbook needs refresh
        if inputs_valid or self._needs_refresh():
            macro_success = self._calculate_pricing()
            if not macro_success:
                # Macro execution failed completely - workbook is corrupted
                calc_time = time.time() - calc_start
                total_time = time.time() - start_time
                logger.error(
                    "[%s] Macro execution failed after retries - triggering session cleanup. Calc time: %.3f, Total time: %.3f",
                    workbook_name,
                    calc_time,
                    total_time,
                )
                raise WorkbookCorruptedError("Macro execution failed after retries - workbook is corrupted and session should be cleaned up")
        calc_time = time.time() - calc_start
        logger.info("[%s] Pricing calculation took %.3f seconds", workbook_name, calc_time)

        # Check if macro execution failed (outputs still contain "refresh data")
        if self._needs_price_calculation():
            error_message = "Macro execution failed - workbook calculation incomplete. Please try again."
            total_time = time.time() - start_time
            logger.error("[%s] %s Total time: %.3f seconds", workbook_name, error_message, total_time)
            return None, error_message

        # Error detection timing
        error_start = time.time()
        error_message = self._detect_error()
        error_time = time.time() - error_start
        logger.info("[%s] Error detection took %.3f seconds", workbook_name, error_time)

        if error_message:
            total_time = time.time() - start_time
            logger.info("[%s] Total process time (with error): %.3f seconds", workbook_name, total_time)
            return None, error_message

        # Output reading timing
        output_start = time.time()
        outputs = self._read_outputs()
        output_time = time.time() - output_start
        logger.info("[%s] Output reading took %.3f seconds", workbook_name, output_time)

        total_time = time.time() - start_time
        logger.info("[%s] Total process time: %.3f seconds", workbook_name, total_time)

        return outputs, None

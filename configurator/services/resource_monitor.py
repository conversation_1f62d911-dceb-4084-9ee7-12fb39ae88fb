"""Resource monitoring service for tracking system CPU and RAM usage."""

import logging
from typing import TYPE_CHECKING

from django.conf import settings

if TYPE_CHECKING:
    import psutil
else:
    try:
        import psutil
    except ImportError:
        psutil = None  # type: ignore

logger = logging.getLogger(__name__)


class ResourceMonitorError(Exception):
    pass


class ResourceMonitor:

    def __init__(self):
        if psutil is None:
            raise ResourceMonitorError("psutil is required for resource monitoring")

        self.max_ram_percent = getattr(settings, "MAX_RAM_PERCENT", 80)
        self.max_cpu_percent = getattr(settings, "MAX_CPU_PERCENT", 90)

    def get_ram_usage_percent(self) -> float:
        try:
            memory = psutil.virtual_memory()
            return memory.percent
        except Exception as e:
            logger.error("Failed to get RAM usage: %s", e)
            raise ResourceMonitorError(f"Failed to get RAM usage: {e}") from e

    def get_cpu_usage_percent(self) -> float:
        try:
            # Get CPU usage over a 1 second interval for more accurate reading
            return psutil.cpu_percent(interval=1)
        except Exception as e:
            logger.error("Failed to get CPU usage: %s", e)
            raise ResourceMonitorError(f"Failed to get CPU usage: {e}") from e

    def check_ram_threshold(self) -> bool:
        try:
            current_ram = self.get_ram_usage_percent()
            return current_ram > self.max_ram_percent
        except ResourceMonitorError:
            # If we can't get RAM usage, assume threshold is not exceeded
            return False

    def check_cpu_threshold(self) -> bool:
        try:
            current_cpu = self.get_cpu_usage_percent()
            return current_cpu > self.max_cpu_percent
        except ResourceMonitorError:
            # If we can't get CPU usage, assume threshold is not exceeded
            return False

    def get_excel_processes(self) -> list:
        try:
            excel_processes = []
            for proc in psutil.process_iter(["pid", "name"]):
                try:
                    if proc.info["name"] and "excel" in proc.info["name"].lower():
                        excel_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return excel_processes
        except Exception as e:
            logger.error("Failed to get Excel processes: %s", e)
            return []

    def get_excel_process_count(self) -> int:
        return len(self.get_excel_processes())

    def get_system_info(self) -> dict:
        try:
            return {
                "ram_usage_percent": self.get_ram_usage_percent(),
                "cpu_usage_percent": self.get_cpu_usage_percent(),
                "excel_process_count": self.get_excel_process_count(),
                "ram_threshold_exceeded": self.check_ram_threshold(),
                "cpu_threshold_exceeded": self.check_cpu_threshold(),
                "max_ram_percent": self.max_ram_percent,
                "max_cpu_percent": self.max_cpu_percent,
            }
        except Exception as e:
            logger.error("Failed to get system info: %s", e)
            # Return partial info with what we can get
            return {
                "ram_usage_percent": 0.0,
                "cpu_usage_percent": 0.0,
                "excel_process_count": 0,
                "ram_threshold_exceeded": False,
                "cpu_threshold_exceeded": False,
                "max_ram_percent": self.max_ram_percent,
                "max_cpu_percent": self.max_cpu_percent,
                "error": str(e),
            }

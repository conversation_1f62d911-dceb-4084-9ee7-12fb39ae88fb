"""
Session management service for handling temporary workbook copies.

This service manages UUID-based temporary workbook copies with proper lifecycle
management, automatic cleanup, and thread-safe operations.
"""

import logging
import shutil
import tempfile
import threading
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import TYPE_CHECKING, Dict, Optional

from django.conf import settings

from configurator.services.excel import ExcelService, get_excel_lock_metrics
from configurator.services.resource_monitor import ResourceMonitor
from configurator.services.workbook import WorkbookService

if TYPE_CHECKING:
    import psutil
else:
    try:
        import psutil
    except ImportError:
        psutil = None  # type: ignore

logger = logging.getLogger(__name__)


class WorkbookSessionError(Exception):
    """Base exception for SessionManager errors."""


class WorkbookSessionService:
    """
    Manages UUID-based temporary workbook copies with proper lifecycle management.

    Handles workbook copying, session tracking, and automatic cleanup of expired sessions.
    Thread-safe for concurrent session operations.

    Singleton pattern: Use get_service() to get the instance.
    """

    _instance: Optional["WorkbookSessionService"] = None
    _instance_lock = threading.Lock()

    def __init__(self, workbook_path: str, temp_dir: Optional[str] = None, session_timeout: int = 300):
        self.workbook_path = Path(workbook_path)
        self.temp_dir = Path(temp_dir) if temp_dir else Path(tempfile.gettempdir())
        self.session_timeout = timedelta(seconds=session_timeout)
        self._sessions_access: Dict[str, datetime] = {}
        self._lock = threading.RLock()

        # Validate master workbook exists
        if not self.workbook_path.exists():
            raise WorkbookSessionError(f"Master workbook not found: {workbook_path}")

        logger.debug("Temporary directory: %s", self.temp_dir)
        logger.debug("Session timeout: %s seconds", session_timeout)

    @classmethod
    def get_service(cls) -> "WorkbookSessionService":
        """
        Get or create the singleton instance of WorkbookSessionService.

        Configuration is automatically loaded from Django settings:
        - EXCEL_WORKBOOK_PATH: Path to the master workbook file
        - EXCEL_SESSION_FILES: Temporary directory for session files
        - EXCEL_SESSION_TIMEOUT: Session timeout in seconds

        Returns:
            The singleton instance of WorkbookSessionService
        """
        with cls._instance_lock:
            if cls._instance is None:
                cls._instance = cls(settings.EXCEL_WORKBOOK_PATH, settings.EXCEL_SESSION_FILES, settings.EXCEL_SESSION_TIMEOUT)

            return cls._instance

    def open_workbook_session(self, session_id: str) -> WorkbookService:
        """
        Open or retrieve an existing workbook session.

        Creates a new temporary workbook copy if the session doesn't exist,
        or returns a fresh WorkbookService instance that creates its own xlwings objects.

        This approach ensures thread safety by never sharing xlwings objects between threads.

        Args:
            session_id: Unique identifier for the session

        Returns:
            WorkbookService instance for the session

        Raises:
            WorkbookSessionError: If workbook operations fail
        """
        with self._lock:
            self.cleanup_expired_sessions()

            # Always create a fresh ExcelService to avoid threading issues
            # xlwings objects should never be shared between threads
            session_workbook_path = Path(self.temp_dir / f"{session_id}.xlsm")

            if not session_workbook_path.exists():
                logger.debug("Creating session workbook: %s", session_workbook_path)
                shutil.copy2(self.workbook_path, session_workbook_path)

            self._sessions_access[session_id] = datetime.now()

            # Connect to existing workbook or open if needed (performance optimized, thread-safe)
            excel_service = ExcelService.open(session_workbook_path, visible=settings.EXCEL_WORKBOOK_VISIBLE)

            return WorkbookService(excel_service)

    def cleanup_session(self, session_id: str) -> bool:
        """
        Clean up a specific session by deleting temporary file.

        Since we no longer store xlwings objects in memory, this method
        only needs to clean up the temporary file and access tracking.

        Args:
            session_id: Unique identifier for the session to clean up

        Returns:
            True if session was found and cleaned up, False if session didn't exist
        """
        with self._lock:
            # Check if we have access tracking for this session
            if session_id not in self._sessions_access:
                return False

            logger.debug("Cleaning up session %s", session_id)

            # Delete the temporary workbook file
            session_workbook_path = Path(self.temp_dir / f"{session_id}.xlsm")
            if session_workbook_path.exists():
                try:
                    session_workbook_path.unlink()
                    logger.debug("Deleted session workbook: %s", session_workbook_path)
                except PermissionError as e:
                    # File is locked by Excel - this is expected with connect-or-open optimization
                    logger.debug("Cannot delete session file (Excel has it open): %s - %s", session_workbook_path, e)
                except Exception as e:
                    logger.warning("Error deleting session file %s: %s", session_workbook_path, e)

            # Remove from access tracking
            del self._sessions_access[session_id]

            logger.debug("Cleaned up session %s", session_id)
            return True

    def force_close_session(self, session_id: str) -> bool:
        """
        Force close a specific session's Excel workbook and clean up files.

        This method attempts to force close any Excel workbook associated with
        the session before cleaning up the session files. Use this when you need
        to ensure the Excel workbook is actually closed, not just disconnected.

        Args:
            session_id: Unique identifier for the session to force close

        Returns:
            True if session was found and cleaned up, False if session didn't exist
        """
        with self._lock:
            # Check if we have access tracking for this session
            if session_id not in self._sessions_access:
                return False

            logger.debug("Force closing session %s", session_id)

            # Try to force close the Excel workbook if it's open
            session_workbook_path = Path(self.temp_dir / f"{session_id}.xlsm")
            try:
                # Attempt to connect to the workbook and force close it
                import xlwings as xw

                try:
                    workbook = xw.Book(str(session_workbook_path))
                    app = workbook.app
                    workbook.close()
                    app.quit()
                    logger.debug("Force closed Excel workbook for session %s", session_id)
                except FileNotFoundError:
                    # Workbook not open, which is fine
                    logger.debug("Workbook not open for session %s", session_id)
                except Exception as e:
                    logger.warning("Could not force close workbook for session %s: %s", session_id, e)
            except Exception as e:
                logger.warning("Error during force close attempt for session %s: %s", session_id, e)

            # Clean up the session file and tracking
            # After force closing Excel, the file should be unlocked
            return self.cleanup_session(session_id)

    def cleanup_expired_sessions(self) -> int:
        """
        Clean up all sessions that have exceeded the timeout period.

        Automatically identifies and removes sessions older than the configured
        session timeout (default: 300 seconds). Also cleans up orphaned files.

        Returns:
            Number of expired sessions that were cleaned up
        """
        with self._lock:
            current_time = datetime.now()
            expired_sessions = []

            # Find expired sessions
            for session_id, access_time in self._sessions_access.items():
                if current_time - access_time > self.session_timeout:
                    expired_sessions.append(session_id)

            # Clean up expired sessions
            for session_id in expired_sessions:
                logger.debug("Session %s expired, cleaning up", session_id)
                self.cleanup_session(session_id)

            self._cleanup_orphaned_files()
            return len(expired_sessions)

    def cleanup_all_sessions(self) -> int:
        """
        Force cleanup of all active sessions and orphaned files.

        Emergency cleanup method that removes all tracked sessions and
        any orphaned .xlsm files in the temporary directory.

        Returns:
            Total number of sessions and orphaned files actually cleaned up
        """
        with self._lock:
            logger.debug("Force cleaning up all sessions")

            # Clean up all tracked sessions and count successful cleanups
            session_ids = list(self._sessions_access.keys())
            sessions_cleaned = 0
            for session_id in session_ids:
                if self.cleanup_session(session_id):
                    sessions_cleaned += 1

            # Clean up any orphaned files
            orphaned_count = self._cleanup_orphaned_files()

            total_cleaned = sessions_cleaned + orphaned_count
            logger.debug("Force cleanup completed: %s sessions and %s orphaned files", sessions_cleaned, orphaned_count)

            return total_cleaned

    def _cleanup_orphaned_files(self) -> int:
        """
        Clean up any .xlsm files in temp directory that aren't tracked sessions.

        Scans the temporary directory for .xlsm files that don't correspond to
        active sessions and removes them. This handles cases where sessions
        weren't properly cleaned up due to crashes or other issues.

        Skips Excel temporary lock files (starting with ~$) and handles
        Windows file locking gracefully.

        Returns:
            Number of orphaned files that were removed
        """
        orphaned_count = 0

        # Find all .xlsm files in temp directory
        for file_path in self.temp_dir.glob("*.xlsm"):
            # Skip Excel temporary lock files (they start with ~$)
            if file_path.name.startswith("~$"):
                logger.debug("Skipping Excel lock file: %s", file_path)
                continue

            potential_session_id = file_path.stem

            if potential_session_id not in self._sessions_access:
                try:
                    file_path.unlink()
                    orphaned_count += 1
                    logger.debug("Removed orphaned file: %s", file_path)
                except PermissionError as e:
                    # File is locked by Excel or another process - skip it
                    logger.debug("Cannot remove file (in use): %s - %s", file_path, e)
                except Exception as e:
                    # Other errors - log but continue
                    logger.warning("Error removing orphaned file %s: %s", file_path, e)

        return orphaned_count

    def cleanup_excel_lock_files(self) -> int:
        """
        Clean up Excel temporary lock files (~$ prefix files).

        These files are created by Excel when workbooks are open and should
        be cleaned up when Excel is closed. This method attempts to remove
        them but gracefully handles cases where they're still locked.

        Returns:
            Number of lock files that were successfully removed
        """
        cleaned_count = 0

        # Find all Excel lock files (start with ~$ and end with .xlsm)
        for file_path in self.temp_dir.glob("~$*.xlsm"):
            try:
                file_path.unlink()
                cleaned_count += 1
                logger.debug("Removed Excel lock file: %s", file_path)
            except PermissionError:
                # File is still locked by Excel - this is normal
                logger.debug("Excel lock file still in use: %s", file_path)
            except Exception as e:
                logger.warning("Error removing Excel lock file %s: %s", file_path, e)

        return cleaned_count

    def cleanup_excel_processes(self) -> int:
        try:
            resource_monitor = ResourceMonitor()
            excel_processes = resource_monitor.get_excel_processes()
        except Exception as e:
            logger.error("Failed to get Excel processes for cleanup: %s", e)
            return 0

        killed_count = 0
        for process in excel_processes:
            try:
                process_name = process.info["name"].lower()
                process_pid = process.info["pid"]

                # Attempt to terminate the process
                process.terminate()

                # Wait for process to terminate and verify success
                try:
                    process.wait(timeout=3)
                    killed_count += 1  # Only count if process actually terminated
                    logger.debug("Successfully terminated Excel process: %s (PID: %s)", process_name, process_pid)
                except psutil.TimeoutExpired:
                    logger.warning("Process didn't terminate gracefully, force killing: %s (PID: %s)", process_name, process_pid)
                    try:
                        process.kill()
                        process.wait(timeout=2)  # Give it a moment to die
                        killed_count += 1  # Count if force kill succeeded
                        logger.debug("Successfully force-killed Excel process: %s (PID: %s)", process_name, process_pid)
                    except Exception as kill_error:
                        logger.warning("Failed to force kill Excel process %s (PID: %s): %s", process_name, process_pid, kill_error)

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                # Process already gone or no permission - don't count as killed
                continue
            except Exception as e:
                logger.warning("Error terminating Excel process: %s", e)
                continue

        return killed_count

    def force_cleanup_all_sessions(self) -> dict:
        """
        Emergency cleanup method for RPC/COM failures.

        When Excel COM server becomes unavailable, this method performs
        aggressive cleanup by killing all Excel processes and cleaning
        up all sessions. This is used when RPC errors indicate the
        Excel COM server is corrupted and needs full recovery.

        Returns:
            dict: Cleanup results with counts of sessions and processes cleaned
        """
        logger.warning("Performing emergency cleanup due to Excel RPC failure")

        try:
            # First, kill all Excel processes to break any stuck COM connections
            excel_killed = self.cleanup_excel_processes()

            # Wait a moment for processes to fully terminate
            time.sleep(1)

            # Clean up all sessions and files
            sessions_cleaned = self.cleanup_all_sessions()

            # Clean up Excel lock files
            lock_files_cleaned = self.cleanup_excel_lock_files()

            logger.info(
                f"Emergency cleanup completed: {sessions_cleaned} sessions, " f"{excel_killed} Excel processes, {lock_files_cleaned} lock files"
            )

            return {"sessions_cleaned": sessions_cleaned, "excel_killed": excel_killed, "lock_files_cleaned": lock_files_cleaned, "status": "success"}

        except Exception as e:
            logger.error("Error during emergency cleanup: %s", e)
            return {"sessions_cleaned": 0, "excel_killed": 0, "lock_files_cleaned": 0, "status": "error", "error": str(e)}

    def health_check(self, test_workbook: bool = False) -> dict:
        """
        Perform comprehensive health check of Excel automation system.

        Tests Excel workbook operations, system resources, and session management.
        Returns detailed status information for monitoring and diagnostics.

        Args:
            test_workbook: Whether to test Excel workbook operations (default: True)

        Returns:
            dict: Health check results including status, system info, and test results
        """

        try:
            resource_monitor = ResourceMonitor()

            # Gather system information
            system_info = resource_monitor.get_system_info()
            session_count = len(self._sessions_access)
            lock_metrics = get_excel_lock_metrics()

            # If workbook testing is disabled, return basic health info
            if not test_workbook:
                return {
                    "status": "ok",
                    "excel_test": "skipped",
                    "test_cell_accessible": None,
                    "system_info": system_info,
                    "session_count": session_count,
                    "excel_lock_metrics": lock_metrics,
                }

            # Perform Excel workbook test
            health_check_session_id = str(uuid.uuid4())
            try:
                workbook_service = self.open_workbook_session(health_check_session_id)

                # Test basic workbook operations
                # Try to read a cell that should have content or test basic read capability
                try:
                    # First try reading the profile cell which might have default content
                    excel_accessible = workbook_service.excel_service.read_cell("profileentry") is not None
                except Exception:
                    excel_accessible = False

                # Clean up the test session
                self.cleanup_session(health_check_session_id)

                return {
                    "status": "ok",
                    "excel_test": "passed",
                    "test_cell_accessible": excel_accessible,
                    "system_info": system_info,
                    "session_count": session_count,
                    "excel_lock_metrics": lock_metrics,
                }

            except Exception as e:
                # Clean up the test session if it was created
                try:
                    self.cleanup_session(health_check_session_id)
                except Exception:
                    pass

                logger.exception("Excel health check failed: %s", e)

                # Still return system info even if Excel test failed
                try:
                    system_info = resource_monitor.get_system_info()
                    session_count = len(self._sessions_access)

                    # Get Excel lock contention metrics even if Excel test failed
                    lock_metrics = get_excel_lock_metrics()
                except Exception:
                    system_info = {"error": "Unable to get system info"}
                    session_count = -1
                    lock_metrics = {"error": "Unable to get lock metrics"}

                return {
                    "status": "error",
                    "excel_test": "failed",
                    "error": str(e),
                    "system_info": system_info,
                    "session_count": session_count,
                    "excel_lock_metrics": lock_metrics,
                }

        except Exception as e:
            logger.exception("Health check endpoint error: %s", e)
            return {
                "status": "error",
                "error": str(e),
            }

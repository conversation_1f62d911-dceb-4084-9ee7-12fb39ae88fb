import os
import subprocess
from functools import wraps
from typing import TypeVar

from django.db.models import Model
from django.http import HttpResponseForbidden

# Generic type variable for model types
ModelType = TypeVar("ModelType", bound=Model)


def strtobool(value: str) -> bool:
    return value.lower() in ("y", "yes", "t", "true", "on", "1")


def is_path_absolute(path):
    return path.startswith("/") or path.startswith("http")


def app_version():
    # Get version from environment variable set in Dockerfile
    # This will already include git branch and commit if using 'latest' tag
    version = os.environ.get("VERSION", "latest")

    # Only try to add git info if it's not already included in the version
    if "-" not in version:
        try:
            branch = subprocess.check_output(["git", "rev-parse", "--abbrev-ref", "HEAD"], stderr=subprocess.DEVNULL).strip().decode("utf-8")
            commit = subprocess.check_output(["git", "rev-parse", "--short", "HEAD"], stderr=subprocess.DEVNULL).strip().decode("utf-8")
            version += f" ({branch}-{commit})"
        except (FileNotFoundError, subprocess.CalledProcessError):
            pass

    return version


def internal_network_required(view_func):
    """
    Decorator that only allows requests from internal network IPs (e.g., Docker network).
    Used for securing endpoints that perform system operations without requiring authentication.
    """

    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):

        if request.headers.get("X-Forwarded-For"):
            return HttpResponseForbidden()

        return view_func(request, *args, **kwargs)

    return wrapped_view

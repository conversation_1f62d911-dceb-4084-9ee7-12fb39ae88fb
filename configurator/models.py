import uuid

from django.contrib.auth import get_user_model
from django.db import models

User = get_user_model()


class APIKey(models.Model):
    key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="api_keys")
    active = models.BooleanField(default=True)

    # Type annotations for Django's automatic attributes
    objects: models.Manager["APIKey"]

    class Meta:
        verbose_name = "API Key"
        verbose_name_plural = "API Keys"

    def __str__(self):
        return f"APIKey for {self.user} ({self.active})"


class ActivityLogType(models.TextChoices):
    INFO = "INFO", "Info"
    WARNING = "WARNING", "Warning"
    ERROR = "ERROR", "Error"
    DEBUG = "DEBUG", "Debug"


class ActivityLog(models.Model):
    text = models.TextField(blank=True, null=True)
    type = models.CharField(max_length=10, choices=ActivityLogType.choices, default=ActivityLogType.INFO)
    data = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    objects: models.Manager["ActivityLog"]

    class Meta:
        abstract = False
        ordering = ("-created_at",)

    def __str__(self):
        return self.summary

    @property
    def summary(self):
        return f"[{self.type}]: {self.text[:100]}{'...' if len(self.text) > 100 else ''}"

# Excel API Development Plan

## Important: Implementation Guidance

**Before implementing any step in this plan, developers and AI agents should refer to `AGENT.md` for detailed implementation guidance, coding standards, and project-specific requirements.**

The `AGENT.md` file contains:
- Project structure and conventions
- Coding standards and best practices
- Testing approaches and requirements
- Configuration and environment setup
- Any project-specific constraints or preferences

This PLAN.md provides the high-level plan and step-by-step prompts, while `AGENT.md` provides the detailed implementation context needed to execute each step correctly.

## Task Management Instructions for AI Agents

**When creating task lists for implementation threads, use this document as the primary source:**

1. **Convert each Step into a main task** (e.g., "Step 1.1: Excel Service Foundation")
2. **Convert each Sub-task into individual task items** (e.g., "1.1.1: Create ExcelService class structure")
3. **Use Dependencies to determine task ordering** - don't start dependent tasks until prerequisites are complete
4. **Use Acceptance Criteria as completion definitions** - tasks are done when all criteria are met
5. **Include the full Detailed Implementation Prompt** as the task description for comprehensive context
6. **Update status indicators** in this document as tasks progress through NOT STARTED → IN PROGRESS → COMPLETE

This structure ensures proper task breakdown, sequencing, and provides all necessary context for high-quality implementation.

---

## Progress Tracking

**Status Legend:**
- `[NOT STARTED]` - Work not yet begun
- `[IN PROGRESS]` - Currently being implemented
- `[COMPLETE]` - Implementation finished and tested
- `[BLOCKED]` - Waiting on dependencies or decisions

**Overall Progress:** 0/12 steps complete (0%)

**Current Phase:** Phase 1 - Core Excel Integration

## Task Management Integration

This document is designed to work seamlessly with AI task management tools:

**For AI Agents:**
1. Each **Step** can be converted into a main task
2. Each **Sub-task** (1.1.1, 1.1.2, etc.) becomes an individual task item
3. **Dependencies** help determine task ordering
4. **Acceptance Criteria** provide clear completion definitions
5. **Detailed Implementation Prompt** contains the full context for implementation

**Task Creation Pattern:**
- Main Task: "Step 1.1: Excel Service Foundation"
- Sub-tasks: "1.1.1: Create ExcelService class structure", etc.
- Use the detailed prompt as the task description
- Mark dependencies to ensure proper sequencing

---

## Project Overview

Building a REST API that automates Excel workbook operations for the Hallite Seal Estimator. The API will:
- Accept form data via REST endpoints
- Write values to Excel cells using xlwings
- Execute Excel macros for calculations
- Return structured JSON responses with pricing and part information
- Manage workbook sessions with proper lifecycle management
- Handle errors and resource constraints

## Architecture Summary

- **Framework**: Django 5.2+ with Django Ninja for REST API
- **Excel Automation**: xlwings for workbook manipulation
- **Error Handling**: pywinauto for VBA dialog capture
- **Session Management**: UUID-based temporary workbook copies
- **Resource Management**: RAM monitoring with email alerts

## Development Phases

### Phase 1: Core Excel Integration (Foundation) `[COMPLETE]`
Build the fundamental Excel automation capabilities without REST API complexity.

### Phase 2: Resource Management & Session Handling `[COMPLETE]`
Implement workbook lifecycle, session management, and resource monitoring.

### Phase 3: REST API Implementation `[NOT STARTED]`
Create the Django Ninja endpoints with proper validation and error handling.

### Phase 4: Integration & Testing `[NOT STARTED]`
Wire everything together and ensure robust operation.

---

## Detailed Implementation Steps

### Phase 1: Core Excel Integration

#### Step 1.1: Excel Service Foundation `[COMPLETED]`

**Sub-tasks:**
- [x] 1.1.1: Create ExcelService class structure and basic configuration
- [x] 1.1.2: Implement workbook open/close operations with COM cleanup
- [x] 1.1.3: Add cell read/write operations with error handling
- [x] 1.1.4: Implement macro execution with timeout and error capture
- [x] 1.1.5: Add comprehensive logging and process cleanup

**Dependencies:** None (foundation step)

**Acceptance Criteria:**
- ExcelService can open/close workbooks without memory leaks
- Cell operations work with proper error handling
- Macro execution respects 5-second timeout
- All Excel processes are properly cleaned up
- Comprehensive logging covers all operations

**Detailed Implementation Prompt:**
```
Create the core Excel automation service that handles basic workbook operations.

Requirements:
- Create an ExcelService class that can open, manipulate, and close Excel workbooks
- Implement methods for reading from and writing to specific cells
- Add support for executing the 'runme' macro with proper error handling
- Include basic logging for debugging Excel operations
- Handle COM errors and Excel process management

Key components to implement:
1. ExcelService class with methods:
   - open_workbook_session(path) -> workbook handle
   - write_cell(workbook, cell_name, value)
   - read_cell(workbook, cell_name) -> value
   - execute_macro(workbook, macro_name)
   - close_workbook(workbook)
   - cleanup_excel_processes()

2. Basic error handling for:
   - Excel not available
   - Workbook file not found
   - Cell reference errors
   - Macro execution failures

3. Configuration management:
   - Read WORKBOOK_PATH from environment
   - Set up logging configuration

Implementation notes:
- Use xlwings for Excel automation
- Implement proper COM object cleanup
- Add delays between cell writes as specified (200ms)
- Set Application.DisplayAlerts=False during macro execution
- Include timeout handling for macro execution (5 second limit)

Testing approach:
- Create unit tests that mock xlwings
- Create integration tests with a test Excel file
- Test error scenarios (missing files, invalid cells, etc.)
```

#### Step 1.2: Workbook State Management `[COMPLETE]`

**Sub-tasks:**
- [x] 1.2.1: Create ConfiguratorWorkbook class with cell mapping configuration
- [x] 1.2.2: Implement input writing sequence with proper delays
- [x] 1.2.3: Add output reading methods for all response sections
- [x] 1.2.4: Implement error detection and message extraction logic
- [x] 1.2.5: Add state validation and business rule checking

**Dependencies:** Step 1.1 (ExcelService) must be complete

**Acceptance Criteria:**
- ExcelService correctly maps business concepts to Excel cells
- Input writing follows exact sequence with 200ms delays
- All output sections are read and structured correctly
- Error conditions are detected and handled per spec
- State validation catches invalid input combinations

**Detailed Implementation Prompt:**
```
Create a ExcelService service that represents the current state of the Excel workbook and provides high-level operations for the business logic.

Requirements:
- Create a ExcelService class that abstracts Excel cell operations into business concepts
- Implement methods for writing input values in the correct sequence
- Add methods for reading all output values after macro execution
- Include validation logic based on workbook state
- Handle the specific error conditions defined in the spec

Key components to implement:
1. ExcelService class with methods:
   - write_inputs(profile, measure, od, id, ch, extra, materials)
   - execute_calculation() -> calls runme macro
   - read_outputs() -> returns structured data
   - validate_state() -> checks for error conditions
   - get_error_message() -> extracts error from partdesc if needed

2. Input writing sequence (with 200ms delays):
   - profileentry → measureentry → ODentry → IDentry → CHentry
   - extratype → extraentry → MATL1entry → MATL2entry → MATL3entry

3. Output reading from cells:
   - Profile outputs: profileentry, C7, measureentry, etc.
   - Part info: C31, partdesc
   - Pricing: costpriceevco, C19, C20, C21
   - Materials: billetreqd1-3, mctime1-3, mmreqd1-3

4. Error detection logic:
   - Check costpriceevco == 0 for sheet errors
   - Check partdesc for specific error messages
   - Handle "refresh data" scenario with automatic retry
   - Detect when materials 2/3 are not applicable (empty Type2/Type3)

Implementation notes:
- Use the ExcelService from Step 1.1 for low-level operations
- Implement proper error handling and logging
- Add validation for input ranges (OD ≤600, ID ≥5, etc.)
- Handle the automatic refresh retry logic
- Map Excel cell names to business concepts

Testing approach:
- Unit tests with mocked ExcelService
- Integration tests with real Excel workbook
- Test all error scenarios defined in spec
- Verify input validation logic
```

#### Step 1.3: Pydantic Models and Validation `[COMPLETE]`

**Sub-tasks:**
- [x] 1.3.1: Create base request/response models matching OpenAPI spec
- [x] 1.3.2: Implement field validators for ranges and formats
- [x] 1.3.3: Add validation enums and constants (profiles, materials, measures)
- [x] 1.3.4: Create dynamic validation logic for workbook-dependent rules
- [x] 1.3.5: Add comprehensive error messages and cross-field validation

**Dependencies:** Step 1.2 (ExcelService) must be complete for dynamic validation

**Acceptance Criteria:**
- All models match OpenAPI spec exactly
- Field validation catches invalid ranges and formats
- Dynamic validation works with workbook state
- Error messages are user-friendly and actionable
- All nullable fields handled correctly

**Detailed Implementation Prompt:**
```
Create the Pydantic models for request/response validation and implement business logic validation that depends on workbook state.

Requirements:
- Define Pydantic models matching the OpenAPI spec exactly
- Implement custom validators for business rules
- Create validation logic that checks workbook state for dynamic rules
- Add proper error messages and field validation

Key components to implement:
1. Request models:
   - EstimateRequest with all fields (profile, measure, od, id, ch, extra, materials, refresh)
   - Field validators for ranges, decimal places, required combinations
   - Custom validator for "extra required when extratype appears" rule

2. Response models:
   - EstimateResponse with nested structure matching spec
   - ProfileOutputs, Measure1/Measure2, Part, Price, Materials models
   - Proper nullable fields for optional materials

3. Validation enums and constants:
   - VALID_PROFILES list
   - VALID_MEASURES list
   - VALID_MATERIALS list with descriptions
   - Validation constants (OD max 600, ID min 5, decimal places)

4. Dynamic validation logic:
   - Validator that checks if extratype is present in workbook state
   - Material validation against available options
   - Cross-field validation (e.g., ID < OD)

Implementation notes:
- Use Pydantic v2 syntax and features
- Implement custom validators using @field_validator and @model_validator
- Add proper error messages that can be surfaced to users
- Handle nullable fields correctly in response models
- Validate decimal precision (3 decimal places)

Testing approach:
- Unit tests for each validator
- Test valid and invalid request combinations
- Test response model serialization
- Verify error messages are user-friendly
```

### Phase 2: Resource Management & Session Handling

#### Step 2.1: Session Management `[IN PROGRESS]`

**Sub-tasks:**
- [x] 2.1.1: Create SessionManager class with basic file operations
- [x] 2.1.2: Implement UUID-based workbook copying and path management
- [x] 2.1.3: Add session lifecycle tracking and timestamp management
- [x] 2.1.4: Implement automatic cleanup of expired sessions
- [x] 2.1.5: Add thread-safe operations and comprehensive error handling

**Dependencies:** Step 1.1 (ExcelService) must be complete

**Acceptance Criteria:**
- Sessions create unique temporary workbook copies
- 5-minute session lifespan enforced automatically
- Thread-safe for concurrent session operations
- Proper cleanup on errors and expiration
- If necessary, add allowances for Windows-specific file handling scenarios

**Detailed Implementation Prompt:**
```
Implement the UUID-based session system for managing temporary workbook copies with proper lifecycle management.

Because of the thorough approach taken with session handling, AVOID using try and catch unless it's a scenario you're designing for, e.g. if the excel close operation fails because an altert window is displaying (although I don't know if this is valid, it's just an example). I want things to to fail if there is an error scenario we've not accounted for.

Requirements:
- Create a SessionManager that handles workbook copying and cleanup
- Implement UUID-based temporary file naming
- Add automatic cleanup of expired sessions (5 minute lifespan)
- Handle concurrent sessions safely
- Manage temporary directory operations

Key components to implement:
1. SessionManager class with methods:
   - create_session(session_id) -> creates temp workbook copy
   - get_session_workbook_path(session_id) -> returns temp file path
   - cleanup_session(session_id) -> removes temp workbook
   - cleanup_expired_sessions() -> removes files older than 5 minutes
   - cleanup_all_sessions() -> emergency cleanup

2. File operations:
   - Copy master workbook to %TEMP%\{uuid}.xlsm
   - Track session creation timestamps
   - Safe file deletion with error handling
   - Directory scanning for cleanup operations

3. Configuration:
   - Read TMP_DIR from environment (default %TEMP%)
   - Read WORKBOOK_PATH for master file
   - Set 5-minute session timeout

4. Error handling:
   - Log all session operations with DEBUG level

Implementation notes:
- Use pathlib for cross-platform path handling
- Implement thread-safe operations for concurrent access
- Add proper logging for session lifecycle events
- Handle Windows-specific temporary directory behavior

DO NOT WRITE TESTS until I give you permission to do so. Then write tests one by one so I can review.

Testing approach:
- Unit tests for session creation/cleanup
- Test concurrent session handling
- Test cleanup of expired sessions
- Integration tests with actual file operations
```

#### Step 2.2: Resource Monitoring and Alerts `[NOT STARTED]`

**Sub-tasks:**
- [ ] 2.2.1: Create ResourceMonitor class with RAM and CPU usage tracking
- [ ] 2.2.2: Use django admins to send email to list if RAM and CPU exceeds reasonable thresholds
- [ ] 2.2.3: Run WorkbookSessionService.cleanup_all_sessions() when thresholds exceeded
- [ ] 2.2.4: Create Python script that can be run by Windows scheduler like cron every 5 minutes

**Dependencies:** Step 2.1 (SessionManager) must be complete for cleanup integration

**Acceptance Criteria:**
- RAM monitoring samples every 30 seconds accurately
- Email alerts sent via Mailgun when thresholds exceeded
- Emergency cleanup terminates Excel processes and purges temp files
- Background service runs reliably without blocking main thread
- Service never denies requests even during cleanup

**Detailed Implementation Prompt:**
```
Implement a very lightweight simple RAM and CPU monitoring with email alerts and emergency cleanup procedures as specified in the resource guardrails.

Requirements:
- Create a ResourceMonitor that tracks system CPU and RAM usage
- Implement email alerting via Mailgun when RAM and CPU exceeds reasonable thresholds
- Add emergency cleanup procedure for high memory and CPU situations
- Create an automated Python script that runs every 5 minutes to check for high memory and CPU usage

Key components to implement:
1. ResourceMonitor class with methods:
   - get_cpu_usage_percent() -> current CPU percentage
   - get_ram_usage_percent() -> current RAM percentage
   - check_ram_threshold() -> compares against MAX_RAM_PERCENT
   - check_cpu_threshold() -> compares against MAX_CPU_PERCENT
   - send_alert_email() -> sends Mailgun email
   - emergency_cleanup() -> terminates Excel processes and purges temp files
   - start_monitoring() -> begins background monitoring

2. Email alerting:
   - Configure Mailgun API client
   - Send <NAME_EMAIL>
   - Use subject "api high-memory alert"
   - <NAME_EMAIL>
   - Include RAM percentage and timestamp in email body

3. Emergency cleanup procedures:
   - Terminate all Excel processes (excel.exe)
   - Delete all *.xlsm files from temp directory
   - Log cleanup actions
   - Continue service operation (never deny requests)

4. Script:
   - Sample RAM and CPU every 5 minutes
   - Compare against MAX_RAM_PERCENT (default 80%) and MAX_CPU_PERCENT (default 90%)
   - Trigger cleanup and alerts if over thresholds
   - Record event in ActivityLog model and log CPU and RAM percentages

Implementation notes:
- Use psutil for cross-platform RAM monitoring
- Implement Mailgun API integration for email alerts
- Use subprocess or psutil for process termination
- Include configuration via environment variables

DO NOT WRITE TESTS until I give you permission to do so. Then write tests one by one so I can review.

Testing approach:
- Unit tests with mocked system resources
- Test email sending functionality
- Test cleanup procedures to check excel process killed and files deleted. Use RAM and CPU thresholds of 0% to ensure cleanup is triggered
- Test monitoring script
```

### Phase 3: REST API Implementation

#### Step 3.1: Django Ninja API Setup `[COMPLETED]`

**Sub-tasks:**
- [x] 3.1.1: Configure Django Ninja API /estimate route in router.py
- [x] 3.1.2: Implement X-API-Key authentication system
- [x] 3.1.4: Add global exception handlers and logging
- [x] 3.1.5: Configure OpenAPI documentation and CORS if needed

**Dependencies:** Step 1.3 (Pydantic Models) must be complete for request/response schemas

**Acceptance Criteria:**
- Django Ninja API properly configured and routed
- X-API-Key authentication works with environment variables
- Consistent error response format matching spec
- Global exception handling for all error types
- OpenAPI spec generated correctly

**Detailed Implementation Prompt:**
```
Set up the Django Ninja REST API with authentication, basic endpoints, and proper error handling structure.

Requirements:
- Configure Django Ninja for the REST API
- Implement API key authentication
- Create basic endpoint structure
- Set up proper error handling and logging
- Configure CORS and other necessary middleware

Key components to implement:
1. Django Ninja API configuration:
   - Create api.py with NinjaAPI instance
   - Configure OpenAPI documentation
   - Set up proper URL routing
   - Add API versioning if needed

2. Authentication:
   - Implement X-API-Key header validation
   - Add proper error responses for invalid keys
   - Read API key from environment variables

3. Basic endpoint structure:
   - /estimate endpoint (POST)
   - /health endpoint (GET, no auth)
   - Proper HTTP status codes
   - Consistent error response format

4. Error handling:
   - Global exception handlers
   - Proper HTTP status codes (422, 500, 503)
   - Consistent error response format matching spec
   - Logging of all errors

Implementation notes:
- Use Django Ninja's built-in features for validation
- Implement proper dependency injection for services
- Add request/response logging
- Configure proper CORS headers if needed
- Set up OpenAPI spec generation

Testing approach:
- Unit tests for authentication
- Test endpoint routing
- Test error handling
- Test OpenAPI spec generation
- Integration tests with test client
```

#### Step 3.2: Health Check Endpoint `[NOT STARTED]`

**Sub-tasks:**
- [x] 3.2.1: Create health check endpoint (/health-check) purely for checking server responsiveness with no authentication that just returns `ok`
- [x] 3.2.2: Create ResourceMonitor class with RAM and CPU usage tracking
- [x] 3.2.3: Create Excel service health check endpoint (/health-check-excel)  with authentication that tests a document can be opened, read, closed and lists RAM, CPU, number of sessions and number of excel processes


#### Step 3.3: Estimate REST API Endpoint `[COMPLETED]`

**Sub-tasks:**
- [x] 3.3.1: Create POST /estimate endpoint with authentication and session handling
- [ ] 3.3.2: 10-second timeout results in the session being destroyed

**Dependencies:** Steps 1.2 (ExcelService), 1.3 (Pydantic Models), 2.1 (SessionManager), and 3.1 (API Setup) must be complete

**Acceptance Criteria:**
- Endpoint accepts EstimateRequest and returns EstimateResponse per spec
- 5-second timeout enforced for macro execution
- Proper cleanup on all error paths

**Detailed Implementation Prompt:**
```
Implement the main /estimate endpoint with complete request processing, validation, Excel automation, and response formatting.

Requirements:
- Create the POST /estimate endpoint with full functionality
- Implement request validation including dynamic workbook-based rules
- Process requests through the complete Excel automation pipeline
- Return properly formatted responses matching the spec exactly

Key components to implement:
1. Estimate endpoint:
   - POST /estimate with authentication required
   - Accept EstimateRequest model
   - Return EstimateResponse model
   - Handle x-configurator-session-id header

2. Request processing pipeline:
   - Validate API key and session ID
   - Create or reuse session workbook
   - Validate request against workbook state
   - Write inputs to Excel in correct sequence
   - Execute macro if refresh=true
   - Read outputs and format response
   - Handle all error scenarios

3. Dynamic validation:
   - Check if extratype is present in workbook
   - Validate extra field is provided when required
   - Validate material selections against available options
   - Check input ranges and formats

4. Error handling:
   - Sheet errors (costpriceevco=0, specific partdesc messages)
   - VBA runtime errors via pywinauto
   - Timeout errors (5 second limit)
   - Validation errors
   - System errors (memory, file access)

Implementation notes:
- Use dependency injection for services
- Implement proper transaction-like behavior
- Add comprehensive logging
- Handle the "refresh data" retry logic automatically
- Ensure proper cleanup on all error paths

Testing approach:
- Unit tests with mocked services
- Integration tests with real Excel workbook
- Test all error scenarios from spec
- Test concurrent requests
- Performance testing for 5-second timeout
```

## Notes for Implementation

- Each step builds incrementally on previous steps
- No orphaned or hanging code - everything integrates
- Focus on robust error handling throughout
- Maintain clean separation between Excel automation and REST API concerns
- Prioritize testability and maintainability
- Follow the exact specifications for request/response formats
- Implement all resource management and monitoring requirements